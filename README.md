# 家族族谱管理系统

一个基于 Spring Boot + JSP + MyBatis + MySQL 的家族族谱管理系统，支持家族成员信息管理、族谱图展示、辈分管理等功能。

## 技术栈

### 后端技术
- **Spring Boot 2.7.0** - 主框架
- **Spring Security** - 安全认证
- **MyBatis** - ORM框架
- **MySQL 8.0** - 数据库
- **Maven** - 项目管理
- **Lombok** - 代码简化
- **JJWT** - JWT令牌处理

### 前端技术
- **JSP + JSTL** - 视图层
- **Tailwind CSS** - CSS框架
- **Animate.css** - 动画效果
- **Font Awesome** - 图标库
- **JavaScript ES6+** - 前端交互

## 功能特性

### 🏠 首页
- 系统介绍和功能展示
- 家族统计信息概览
- 响应式设计，支持移动端

### 👥 成员管理
- 添加、编辑、删除家族成员
- 成员信息包括：姓名、性别、出生/死亡日期、照片、描述等
- 支持成员搜索和筛选
- 分页显示成员列表

### 🌳 族谱图
- 可视化家族关系图
- 按辈分层级展示
- 支持成员详情查看
- 响应式族谱布局

### 📊 辈分管理
- 按辈分统计成员数量
- 辈分成员列表查看
- 辈分信息管理

### 🔐 用户系统
- 用户注册和登录
- JWT令牌认证
- 密码加密存储
- 登录状态管理

## 项目结构

```
family-tree-system/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/familytree/
│   │   │       ├── FamilyTreeApplication.java          # 启动类
│   │   │       ├── config/                            # 配置类
│   │   │       │   ├── SecurityConfig.java           # 安全配置
│   │   │       │   └── WebConfig.java                # Web配置
│   │   │       ├── controller/                       # 控制器
│   │   │       │   ├── FamilyMemberController.java   # 成员API
│   │   │       │   ├── PageController.java           # 页面控制器
│   │   │       │   └── UserController.java           # 用户API
│   │   │       ├── entity/                           # 实体类
│   │   │       │   ├── FamilyMember.java             # 家族成员
│   │   │       │   └── User.java                     # 用户
│   │   │       ├── mapper/                           # 数据访问层
│   │   │       │   ├── FamilyMemberMapper.java       # 成员Mapper
│   │   │       │   └── UserMapper.java               # 用户Mapper
│   │   │       └── service/                          # 服务层
│   │   │           ├── FamilyMemberService.java      # 成员服务接口
│   │   │           ├── UserService.java              # 用户服务接口
│   │   │           └── impl/                         # 服务实现
│   │   │               ├── FamilyMemberServiceImpl.java
│   │   │               └── UserServiceImpl.java
│   │   ├── resources/
│   │   │   ├── application.yml                       # 应用配置
│   │   │   ├── schema.sql                            # 数据库初始化脚本
│   │   │   └── mapper/                               # MyBatis映射文件
│   │   │       ├── FamilyMemberMapper.xml
│   │   │       └── UserMapper.xml
│   │   └── webapp/
│   │       ├── static/                               # 静态资源
│   │       │   ├── css/
│   │       │   │   └── style.css                     # 自定义样式
│   │       │   └── js/
│   │       │       └── common.js                     # 公共JS函数
│   │       └── WEB-INF/
│   │           └── views/                            # JSP页面
│   │               ├── index.jsp                     # 首页
│   │               ├── login.jsp                     # 登录页
│   │               ├── register.jsp                  # 注册页
│   │               ├── family-tree.jsp               # 族谱图
│   │               ├── member-management.jsp         # 成员管理
│   │               └── generation-management.jsp     # 辈分管理
│   └── test/                                         # 测试代码
├── pom.xml                                           # Maven配置
└── README.md                                         # 项目说明
```

## 快速开始

### 环境要求
- JDK 8 或更高版本
- Maven 3.6+
- MySQL 8.0+
- IDE（推荐 IntelliJ IDEA 或 Eclipse）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd family-tree-system
   ```

2. **创建数据库**
   ```sql
   CREATE DATABASE family_tree CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **配置数据库连接**
   
   编辑 `src/main/resources/application.yml` 文件，修改数据库连接信息：
   ```yaml
   spring:
     datasource:
       url: ************************************************************************************************************************
       username: your_username
       password: your_password
   ```

4. **初始化数据库**
   
   执行 `src/main/resources/schema.sql` 中的SQL脚本来创建表结构和初始数据。

5. **编译和运行**
   ```bash
   # 编译项目
   mvn clean compile
   
   # 运行项目
   mvn spring-boot:run
   ```

6. **访问应用**
   
   打开浏览器访问：http://localhost:8080

### 默认账户
- 用户名：admin
- 密码：admin123

## API接口

### 用户相关
- `POST /api/users/login` - 用户登录
- `POST /api/users/register` - 用户注册
- `POST /api/users/verify-token` - 验证令牌

### 家族成员相关
- `GET /api/family-members` - 获取所有成员
- `GET /api/family-members/{id}` - 获取指定成员
- `POST /api/family-members` - 添加成员
- `PUT /api/family-members/{id}` - 更新成员
- `DELETE /api/family-members/{id}` - 删除成员
- `GET /api/family-members/generations` - 获取所有辈分
- `GET /api/family-members/generation/{generationName}` - 获取指定辈分成员
- `GET /api/family-members/stats` - 获取统计信息
- `GET /api/family-members/search` - 搜索成员

### 关系管理
- `POST /api/family-members/{id}/children` - 添加子女
- `POST /api/family-members/{id}/siblings` - 添加兄弟姐妹
- `POST /api/family-members/{id}/parents` - 添加父母

## 数据库设计

### 用户表 (users)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 主键，自增 |
| username | VARCHAR(50) | 用户名，唯一 |
| password | VARCHAR(255) | 密码（加密） |
| role | VARCHAR(20) | 角色 |
| created_at | TIMESTAMP | 创建时间 |

### 家族成员表 (family_members)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 主键，自增 |
| name | VARCHAR(100) | 姓名 |
| gender | ENUM | 性别（MALE/FEMALE/UNKNOWN） |
| birth_date | DATE | 出生日期 |
| death_date | DATE | 死亡日期 |
| father_id | BIGINT | 父亲ID |
| mother_id | BIGINT | 母亲ID |
| spouse_id | BIGINT | 配偶ID |
| photo_url | VARCHAR(255) | 照片URL |
| description | TEXT | 描述 |
| generation_name | VARCHAR(50) | 辈分名称 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 开发指南

### 添加新功能
1. 在对应的 Service 接口中定义方法
2. 在 ServiceImpl 中实现业务逻辑
3. 在 Mapper 接口中定义数据访问方法
4. 在 Mapper.xml 中编写SQL语句
5. 在 Controller 中添加API接口
6. 在前端页面中调用API

### 代码规范
- 使用 Lombok 注解减少样板代码
- 遵循 RESTful API 设计规范
- 使用统一的异常处理
- 添加适当的日志记录
- 编写单元测试

### 安全考虑
- 所有密码使用 BCrypt 加密
- 使用 JWT 进行身份认证
- 输入数据验证和过滤
- SQL注入防护
- XSS攻击防护

## 部署说明

### 开发环境
```bash
mvn spring-boot:run
```

### 生产环境
1. **打包应用**
   ```bash
   mvn clean package
   ```

2. **运行JAR包**
   ```bash
   java -jar target/family-tree-system-1.0.0.jar
   ```

3. **使用Docker部署**
   ```dockerfile
   FROM openjdk:8-jre-slim
   COPY target/family-tree-system-1.0.0.jar app.jar
   EXPOSE 8080
   ENTRYPOINT ["java", "-jar", "/app.jar"]
   ```

## 常见问题

### Q: 启动时提示数据库连接失败？
A: 请检查 `application.yml` 中的数据库配置信息，确保MySQL服务已启动且连接信息正确。

### Q: 页面样式显示异常？
A: 请确保静态资源路径配置正确，检查 `WebConfig.java` 中的资源映射配置。

### Q: JWT令牌验证失败？
A: 请检查 `application.yml` 中的JWT密钥配置，确保前后端使用相同的密钥。

### Q: 上传的图片无法显示？
A: 请检查图片上传路径和访问权限，确保Web服务器可以访问图片文件。

## 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/your-username/family-tree-system

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基本的家族成员管理功能
- 支持族谱图可视化展示
- 用户认证和权限管理
- 响应式Web界面

---

感谢使用家族族谱管理系统！🎉
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家族族谱管理系统 - 项目展示</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-5xl font-bold mb-4">
                <i class="fas fa-tree mr-3"></i>
                家族族谱管理系统
            </h1>
            <p class="text-xl opacity-90">基于 Spring Boot + Vue 技术栈的现代化家族管理平台</p>
        </div>
    </header>

    <!-- Status Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-bold text-lg">项目重构完成！</h3>
                            <p>已成功将项目转换为 Spring Boot + JSP + MyBatis + MySQL 架构</p>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-6 py-4 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-bold text-lg">当前状态</h3>
                            <p>由于网络问题，Maven 依赖下载受阻。项目结构已完成，可手动配置启动。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tech Stack -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">
                <i class="fas fa-cogs mr-3"></i>
                技术栈架构
            </h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                <div class="card-hover bg-white p-6 rounded-lg shadow-lg border">
                    <div class="text-center">
                        <i class="fab fa-java text-4xl text-orange-500 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">Spring Boot 2.7.0</h3>
                        <p class="text-gray-600">主框架，提供自动配置和嵌入式服务器</p>
                    </div>
                </div>
                <div class="card-hover bg-white p-6 rounded-lg shadow-lg border">
                    <div class="text-center">
                        <i class="fas fa-database text-4xl text-blue-500 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">MyBatis + MySQL</h3>
                        <p class="text-gray-600">ORM框架 + 关系型数据库</p>
                    </div>
                </div>
                <div class="card-hover bg-white p-6 rounded-lg shadow-lg border">
                    <div class="text-center">
                        <i class="fas fa-shield-alt text-4xl text-green-500 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">Spring Security</h3>
                        <p class="text-gray-600">安全框架，JWT认证</p>
                    </div>
                </div>
                <div class="card-hover bg-white p-6 rounded-lg shadow-lg border">
                    <div class="text-center">
                        <i class="fas fa-code text-4xl text-purple-500 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">JSP + JSTL</h3>
                        <p class="text-gray-600">服务端渲染视图技术</p>
                    </div>
                </div>
                <div class="card-hover bg-white p-6 rounded-lg shadow-lg border">
                    <div class="text-center">
                        <i class="fas fa-paint-brush text-4xl text-pink-500 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">Tailwind CSS</h3>
                        <p class="text-gray-600">现代化CSS框架</p>
                    </div>
                </div>
                <div class="card-hover bg-white p-6 rounded-lg shadow-lg border">
                    <div class="text-center">
                        <i class="fas fa-tools text-4xl text-gray-500 mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">Maven + Lombok</h3>
                        <p class="text-gray-600">项目管理 + 代码简化</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">
                <i class="fas fa-star mr-3"></i>
                核心功能
            </h2>
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold mb-3 text-blue-600">
                        <i class="fas fa-users mr-2"></i>
                        成员管理
                    </h3>
                    <ul class="text-gray-600 space-y-2">
                        <li>• 添加、编辑、删除家族成员</li>
                        <li>• 成员信息详细管理</li>
                        <li>• 关系链建立和维护</li>
                        <li>• 搜索和筛选功能</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold mb-3 text-green-600">
                        <i class="fas fa-sitemap mr-2"></i>
                        族谱图展示
                    </h3>
                    <ul class="text-gray-600 space-y-2">
                        <li>• 可视化家族关系图</li>
                        <li>• 交互式族谱浏览</li>
                        <li>• 多代关系展示</li>
                        <li>• 响应式设计适配</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold mb-3 text-purple-600">
                        <i class="fas fa-layer-group mr-2"></i>
                        辈分管理
                    </h3>
                    <ul class="text-gray-600 space-y-2">
                        <li>• 按辈分统计成员</li>
                        <li>• 辈分关系管理</li>
                        <li>• 统计数据展示</li>
                        <li>• 辈分信息维护</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold mb-3 text-red-600">
                        <i class="fas fa-lock mr-2"></i>
                        用户系统
                    </h3>
                    <ul class="text-gray-600 space-y-2">
                        <li>• 用户注册和登录</li>
                        <li>• JWT令牌认证</li>
                        <li>• 权限控制管理</li>
                        <li>• 安全访问保护</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Structure -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">
                <i class="fas fa-folder-tree mr-3"></i>
                项目结构
            </h2>
            <div class="max-w-4xl mx-auto">
                <div class="bg-gray-900 text-green-400 p-6 rounded-lg font-mono text-sm overflow-x-auto">
<pre>family-tree-system/
├── src/main/java/com/familytree/
│   ├── FamilyTreeApplication.java          # 主启动类
│   ├── config/                             # 配置类
│   │   ├── SecurityConfig.java             # Spring Security配置
│   │   └── WebConfig.java                  # Web MVC配置
│   ├── controller/                         # 控制器层
│   │   ├── UserController.java             # 用户控制器
│   │   ├── FamilyMemberController.java     # 成员控制器
│   │   └── PageController.java             # 页面控制器
│   ├── service/                            # 服务层
│   │   ├── UserService.java                # 用户服务接口
│   │   ├── FamilyMemberService.java        # 成员服务接口
│   │   └── impl/                           # 服务实现
│   ├── mapper/                             # MyBatis映射器
│   │   ├── UserMapper.java                 # 用户映射器
│   │   └── FamilyMemberMapper.java         # 成员映射器
│   ├── entity/                             # 实体类
│   │   ├── User.java                       # 用户实体
│   │   └── FamilyMember.java               # 成员实体
│   └── util/                               # 工具类
│       └── JwtUtil.java                    # JWT工具类
├── src/main/resources/
│   ├── application.properties              # 应用配置
│   ├── mapper/                             # MyBatis XML映射
│   └── schema.sql                          # 数据库初始化脚本
├── src/main/webapp/
│   ├── WEB-INF/views/                      # JSP页面
│   │   ├── index.jsp                       # 首页
│   │   ├── login.jsp                       # 登录页
│   │   ├── register.jsp                    # 注册页
│   │   ├── family-tree.jsp                 # 族谱图页
│   │   ├── member-management.jsp           # 成员管理页
│   │   └── generation-management.jsp       # 辈分管理页
│   ├── static/
│   │   ├── css/style.css                   # 样式文件
│   │   └── js/common.js                    # 公共JS
│   └── test.html                           # 测试页面
└── pom.xml                                 # Maven配置</pre>
                </div>
            </div>
        </div>
    </section>

    <!-- Next Steps -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-800">
                <i class="fas fa-rocket mr-3"></i>
                启动指南
            </h2>
            <div class="max-w-4xl mx-auto">
                <div class="bg-white p-8 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold mb-6 text-blue-600">快速启动步骤：</h3>
                    <ol class="space-y-4 text-gray-700">
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                            <div>
                                <strong>配置数据库：</strong>
                                <p class="text-sm text-gray-600 mt-1">修改 application.properties 中的数据库连接信息</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                            <div>
                                <strong>初始化数据库：</strong>
                                <p class="text-sm text-gray-600 mt-1">执行 src/main/resources/schema.sql 脚本</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                            <div>
                                <strong>启动应用：</strong>
                                <p class="text-sm text-gray-600 mt-1">运行 <code class="bg-gray-100 px-2 py-1 rounded">mvn spring-boot:run</code></p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                            <div>
                                <strong>访问应用：</strong>
                                <p class="text-sm text-gray-600 mt-1">打开浏览器访问 <code class="bg-gray-100 px-2 py-1 rounded">http://localhost:8080</code></p>
                            </div>
                        </li>
                    </ol>
                    
                    <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h4 class="font-bold text-yellow-800 mb-2">
                            <i class="fas fa-lightbulb mr-2"></i>
                            默认登录信息：
                        </h4>
                        <p class="text-yellow-700">
                            用户名：<code class="bg-yellow-100 px-2 py-1 rounded">admin</code> | 
                            密码：<code class="bg-yellow-100 px-2 py-1 rounded">admin123</code>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="gradient-bg text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="text-lg">
                <i class="fas fa-heart text-red-300 mr-2"></i>
                家族族谱管理系统 - 传承家族文化，连接血脉亲情
            </p>
            <p class="text-sm opacity-75 mt-2">基于 Spring Boot + JSP + MyBatis + MySQL 技术栈构建</p>
        </div>
    </footer>
</body>
</html>
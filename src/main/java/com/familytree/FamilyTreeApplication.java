package com.familytree;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * 家族族谱管理系统主启动类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@SpringBootApplication
public class FamilyTreeApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(FamilyTreeApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(FamilyTreeApplication.class, args);
    }
}
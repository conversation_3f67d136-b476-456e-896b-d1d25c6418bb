package com.familytree.controller;

import com.familytree.entity.FamilyMember;
import com.familytree.service.FamilyMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 家族成员控制器
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class FamilyMemberController {
    
    @Autowired
    private FamilyMemberService familyMemberService;
    
    /**
     * 获取所有家族成员
     */
    @GetMapping("/members")
    public ResponseEntity<Map<String, Object>> getAllMembers() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<FamilyMember> members = familyMemberService.getAllMembers();
            response.put("success", true);
            response.put("data", members);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取家族成员列表失败", e);
            response.put("success", false);
            response.put("message", "获取家族成员列表失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 根据ID获取家族成员
     */
    @GetMapping("/members/{id}")
    public ResponseEntity<Map<String, Object>> getMemberById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            FamilyMember member = familyMemberService.getMemberById(id);
            if (member != null) {
                response.put("success", true);
                response.put("data", member);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "家族成员不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取家族成员失败: {}", id, e);
            response.put("success", false);
            response.put("message", "获取家族成员失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 添加家族成员
     */
    @PostMapping("/members")
    public ResponseEntity<Map<String, Object>> addMember(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            FamilyMember member = buildMemberFromRequest(request);
            
            boolean success = familyMemberService.addMember(member);
            if (success) {
                response.put("success", true);
                response.put("message", "添加家族成员成功");
                response.put("data", member);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加家族成员失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加家族成员失败", e);
            response.put("success", false);
            response.put("message", "添加家族成员失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 更新家族成员
     */
    @PutMapping("/members/{id}")
    public ResponseEntity<Map<String, Object>> updateMember(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            FamilyMember member = buildMemberFromRequest(request);
            member.setId(id);
            
            boolean success = familyMemberService.updateMember(member);
            if (success) {
                response.put("success", true);
                response.put("message", "更新家族成员成功");
                response.put("data", member);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "更新家族成员失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("更新家族成员失败: {}", id, e);
            response.put("success", false);
            response.put("message", "更新家族成员失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 删除家族成员
     */
    @DeleteMapping("/members/{id}")
    public ResponseEntity<Map<String, Object>> deleteMember(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = familyMemberService.deleteMember(id);
            if (success) {
                response.put("success", true);
                response.put("message", "删除家族成员成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "删除家族成员失败，可能存在关联数据");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("删除家族成员失败: {}", id, e);
            response.put("success", false);
            response.put("message", "删除家族成员失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取所有辈分
     */
    @GetMapping("/generations")
    public ResponseEntity<Map<String, Object>> getAllGenerations() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> generations = familyMemberService.getAllGenerations();
            response.put("success", true);
            response.put("data", generations);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取辈分列表失败", e);
            response.put("success", false);
            response.put("message", "获取辈分列表失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> stats = familyMemberService.getStatistics();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取统计信息失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 添加子女
     */
    @PostMapping("/members/{id}/add-child")
    public ResponseEntity<Map<String, Object>> addChild(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            FamilyMember child = buildMemberFromRequest(request);
            
            boolean success = familyMemberService.addChild(id, child);
            if (success) {
                response.put("success", true);
                response.put("message", "添加子女成功");
                response.put("data", child);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加子女失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加子女失败: parentId={}", id, e);
            response.put("success", false);
            response.put("message", "添加子女失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 添加兄弟姐妹
     */
    @PostMapping("/members/{id}/add-sibling")
    public ResponseEntity<Map<String, Object>> addSibling(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            FamilyMember sibling = buildMemberFromRequest(request);
            
            boolean success = familyMemberService.addSibling(id, sibling);
            if (success) {
                response.put("success", true);
                response.put("message", "添加兄弟姐妹成功");
                response.put("data", sibling);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加兄弟姐妹失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加兄弟姐妹失败: siblingId={}", id, e);
            response.put("success", false);
            response.put("message", "添加兄弟姐妹失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 添加父母
     */
    @PostMapping("/members/{id}/add-parent")
    public ResponseEntity<Map<String, Object>> addParent(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            FamilyMember parent = buildMemberFromRequest(request);
            
            boolean success = familyMemberService.addParent(id, parent);
            if (success) {
                response.put("success", true);
                response.put("message", "添加父母成功");
                response.put("data", parent);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加父母失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加父母失败: childId={}", id, e);
            response.put("success", false);
            response.put("message", "添加父母失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 从请求构建家族成员对象
     */
    private FamilyMember buildMemberFromRequest(Map<String, Object> request) {
        FamilyMember member = new FamilyMember();
        
        if (request.get("name") != null) {
            member.setName(request.get("name").toString());
        }
        
        if (request.get("birthDate") != null && !request.get("birthDate").toString().isEmpty()) {
            member.setBirthDate(LocalDate.parse(request.get("birthDate").toString()));
        }
        
        if (request.get("deathDate") != null && !request.get("deathDate").toString().isEmpty()) {
            member.setDeathDate(LocalDate.parse(request.get("deathDate").toString()));
        }
        
        if (request.get("fatherId") != null && !request.get("fatherId").toString().isEmpty()) {
            member.setFatherId(Long.valueOf(request.get("fatherId").toString()));
        }
        
        if (request.get("motherId") != null && !request.get("motherId").toString().isEmpty()) {
            member.setMotherId(Long.valueOf(request.get("motherId").toString()));
        }
        
        if (request.get("spouseId") != null && !request.get("spouseId").toString().isEmpty()) {
            member.setSpouseId(Long.valueOf(request.get("spouseId").toString()));
        }
        
        if (request.get("gender") != null) {
            member.setGender(request.get("gender").toString());
        }
        
        if (request.get("photoUrl") != null) {
            member.setPhotoUrl(request.get("photoUrl").toString());
        }
        
        if (request.get("description") != null) {
            member.setDescription(request.get("description").toString());
        }
        
        if (request.get("generationName") != null) {
            member.setGenerationName(request.get("generationName").toString());
        }
        
        return member;
    }
}
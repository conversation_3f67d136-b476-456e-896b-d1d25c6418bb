package com.familytree.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 页面控制器
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Controller
public class PageController {
    
    /**
     * 首页
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("title", "族谱管理系统");
        return "index";
    }
    
    /**
     * 首页
     */
    @GetMapping("/index")
    public String indexPage(Model model) {
        model.addAttribute("title", "族谱管理系统");
        return "index";
    }
    
    /**
     * 族谱图页面
     */
    @GetMapping("/family-tree")
    public String familyTree(Model model) {
        model.addAttribute("title", "族谱图 - 族谱管理系统");
        return "family-tree";
    }
    
    /**
     * 成员管理页面
     */
    @GetMapping("/member-management")
    public String memberManagement(Model model) {
        model.addAttribute("title", "成员管理 - 族谱管理系统");
        return "member-management";
    }
    
    /**
     * 辈分管理页面
     */
    @GetMapping("/generation-management")
    public String generationManagement(Model model) {
        model.addAttribute("title", "辈分管理 - 族谱管理系统");
        return "generation-management";
    }
    
    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String login(Model model) {
        model.addAttribute("title", "登录 - 族谱管理系统");
        return "login";
    }
    
    /**
     * 注册页面
     */
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("title", "注册 - 族谱管理系统");
        return "register";
    }
}
package com.familytree.controller;

import com.familytree.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String username = request.get("username");
            String password = request.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (password == null || password.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "密码不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String token = userService.login(username.trim(), password);
            
            if (token != null) {
                response.put("success", true);
                response.put("message", "登录成功");
                response.put("token", token);
                log.info("用户登录成功: {}", username);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "用户名或密码错误");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("登录处理异常", e);
            response.put("success", false);
            response.put("message", "登录失败，请稍后重试");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String username = request.get("username");
            String password = request.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (password == null || password.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "密码不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (username.trim().length() < 3) {
                response.put("success", false);
                response.put("message", "用户名长度不能少于3个字符");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (password.length() < 6) {
                response.put("success", false);
                response.put("message", "密码长度不能少于6个字符");
                return ResponseEntity.badRequest().body(response);
            }
            
            boolean success = userService.register(username.trim(), password);
            
            if (success) {
                response.put("success", true);
                response.put("message", "注册成功");
                log.info("用户注册成功: {}", username);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "用户名已存在");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("注册处理异常", e);
            response.put("success", false);
            response.put("message", "注册失败，请稍后重试");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 验证令牌
     */
    @PostMapping("/verify-token")
    public ResponseEntity<Map<String, Object>> verifyToken(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                response.put("valid", false);
                response.put("message", "无效的令牌格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            String token = authHeader.substring(7);
            Long userId = userService.validateToken(token);
            
            if (userId != null) {
                response.put("valid", true);
                response.put("userId", userId);
                return ResponseEntity.ok(response);
            } else {
                response.put("valid", false);
                response.put("message", "令牌已过期或无效");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("令牌验证异常", e);
            response.put("valid", false);
            response.put("message", "令牌验证失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
package com.familytree.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 家族成员实体类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FamilyMember {
    
    /**
     * 成员ID
     */
    private Long id;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 出生日期
     */
    private LocalDate birthDate;
    
    /**
     * 死亡日期
     */
    private LocalDate deathDate;
    
    /**
     * 父亲ID
     */
    private Long fatherId;
    
    /**
     * 母亲ID
     */
    private Long motherId;
    
    /**
     * 配偶ID
     */
    private Long spouseId;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 照片URL
     */
    private String photoUrl;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 辈分名称
     */
    private String generationName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
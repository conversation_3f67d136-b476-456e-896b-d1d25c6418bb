package com.familytree.mapper;

import com.familytree.entity.FamilyMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 家族成员数据访问层接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface FamilyMemberMapper {
    
    /**
     * 查询所有家族成员
     * 
     * @return 家族成员列表
     */
    List<FamilyMember> findAll();
    
    /**
     * 根据ID查找家族成员
     * 
     * @param id 成员ID
     * @return 家族成员信息
     */
    FamilyMember findById(@Param("id") Long id);
    
    /**
     * 根据辈分名称查询成员
     * 
     * @param generationName 辈分名称
     * @return 家族成员列表
     */
    List<FamilyMember> findByGenerationName(@Param("generationName") String generationName);
    
    /**
     * 根据父亲ID查询子女
     * 
     * @param fatherId 父亲ID
     * @return 家族成员列表
     */
    List<FamilyMember> findByFatherId(@Param("fatherId") Long fatherId);
    
    /**
     * 根据母亲ID查询子女
     * 
     * @param motherId 母亲ID
     * @return 家族成员列表
     */
    List<FamilyMember> findByMotherId(@Param("motherId") Long motherId);
    
    /**
     * 插入新成员
     * 
     * @param member 家族成员信息
     * @return 影响行数
     */
    int insert(FamilyMember member);
    
    /**
     * 更新成员信息
     * 
     * @param member 家族成员信息
     * @return 影响行数
     */
    int update(FamilyMember member);
    
    /**
     * 删除成员
     * 
     * @param id 成员ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 获取所有辈分名称
     * 
     * @return 辈分名称列表
     */
    List<String> findAllGenerationNames();
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getStatistics();
    
    /**
     * 根据姓名模糊查询
     * 
     * @param name 姓名关键字
     * @return 家族成员列表
     */
    List<FamilyMember> findByNameLike(@Param("name") String name);
}
package com.familytree.service;

import com.familytree.entity.FamilyMember;

import java.util.List;
import java.util.Map;

/**
 * 家族成员服务接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
public interface FamilyMemberService {
    
    /**
     * 获取所有家族成员
     * 
     * @return 家族成员列表
     */
    List<FamilyMember> getAllMembers();
    
    /**
     * 根据ID获取家族成员
     * 
     * @param id 成员ID
     * @return 家族成员信息
     */
    FamilyMember getMemberById(Long id);
    
    /**
     * 根据辈分名称获取成员
     * 
     * @param generationName 辈分名称
     * @return 家族成员列表
     */
    List<FamilyMember> getMembersByGeneration(String generationName);
    
    /**
     * 添加家族成员
     * 
     * @param member 家族成员信息
     * @return 添加结果
     */
    boolean addMember(FamilyMember member);
    
    /**
     * 更新家族成员信息
     * 
     * @param member 家族成员信息
     * @return 更新结果
     */
    boolean updateMember(FamilyMember member);
    
    /**
     * 删除家族成员
     * 
     * @param id 成员ID
     * @return 删除结果
     */
    boolean deleteMember(Long id);
    
    /**
     * 获取所有辈分名称
     * 
     * @return 辈分名称列表
     */
    List<String> getAllGenerations();
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getStatistics();
    
    /**
     * 根据姓名搜索成员
     * 
     * @param name 姓名关键字
     * @return 家族成员列表
     */
    List<FamilyMember> searchMembersByName(String name);
    
    /**
     * 为成员添加子女
     * 
     * @param parentId 父母ID
     * @param child 子女信息
     * @return 添加结果
     */
    boolean addChild(Long parentId, FamilyMember child);
    
    /**
     * 为成员添加兄弟姐妹
     * 
     * @param siblingId 兄弟姐妹ID
     * @param sibling 兄弟姐妹信息
     * @return 添加结果
     */
    boolean addSibling(Long siblingId, FamilyMember sibling);
    
    /**
     * 为成员添加父母
     * 
     * @param childId 子女ID
     * @param parent 父母信息
     * @return 添加结果
     */
    boolean addParent(Long childId, FamilyMember parent);
}
package com.familytree.service;

import com.familytree.entity.User;

/**
 * 用户服务接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
public interface UserService {
    
    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return JWT令牌
     */
    String login(String username, String password);
    
    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码
     * @return 注册结果
     */
    boolean register(String username, String password);
    
    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);
    
    /**
     * 根据ID查找用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    User findById(Long id);
    
    /**
     * 验证JWT令牌
     * 
     * @param token JWT令牌
     * @return 用户ID
     */
    Long validateToken(String token);
    
    /**
     * 生成JWT令牌
     * 
     * @param userId 用户ID
     * @return JWT令牌
     */
    String generateToken(Long userId);
}
package com.familytree.service.impl;

import com.familytree.entity.FamilyMember;
import com.familytree.mapper.FamilyMemberMapper;
import com.familytree.service.FamilyMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 家族成员服务实现类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Service
public class FamilyMemberServiceImpl implements FamilyMemberService {
    
    @Autowired
    private FamilyMemberMapper familyMemberMapper;
    
    @Override
    public List<FamilyMember> getAllMembers() {
        try {
            return familyMemberMapper.findAll();
        } catch (Exception e) {
            log.error("获取所有家族成员失败", e);
            throw new RuntimeException("获取家族成员列表失败", e);
        }
    }
    
    @Override
    public FamilyMember getMemberById(Long id) {
        try {
            return familyMemberMapper.findById(id);
        } catch (Exception e) {
            log.error("根据ID获取家族成员失败: {}", id, e);
            return null;
        }
    }
    
    @Override
    public List<FamilyMember> getMembersByGeneration(String generationName) {
        try {
            return familyMemberMapper.findByGenerationName(generationName);
        } catch (Exception e) {
            log.error("根据辈分获取家族成员失败: {}", generationName, e);
            throw new RuntimeException("获取辈分成员失败", e);
        }
    }
    
    @Override
    @Transactional
    public boolean addMember(FamilyMember member) {
        try {
            member.setCreatedAt(LocalDateTime.now());
            member.setUpdatedAt(LocalDateTime.now());
            
            int result = familyMemberMapper.insert(member);
            if (result > 0) {
                log.info("添加家族成员成功: {}", member.getName());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("添加家族成员失败: {}", member.getName(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean updateMember(FamilyMember member) {
        try {
            member.setUpdatedAt(LocalDateTime.now());
            
            int result = familyMemberMapper.update(member);
            if (result > 0) {
                log.info("更新家族成员成功: {}", member.getName());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新家族成员失败: {}", member.getName(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean deleteMember(Long id) {
        try {
            FamilyMember member = familyMemberMapper.findById(id);
            if (member == null) {
                log.warn("要删除的家族成员不存在: {}", id);
                return false;
            }
            
            // 检查是否有子女引用此成员作为父母
            List<FamilyMember> children = familyMemberMapper.findByFatherId(id);
            children.addAll(familyMemberMapper.findByMotherId(id));
            
            if (!children.isEmpty()) {
                log.warn("无法删除家族成员，存在子女关联: {}", member.getName());
                return false;
            }
            
            int result = familyMemberMapper.deleteById(id);
            if (result > 0) {
                log.info("删除家族成员成功: {}", member.getName());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除家族成员失败: {}", id, e);
            return false;
        }
    }
    
    @Override
    public List<String> getAllGenerations() {
        try {
            return familyMemberMapper.findAllGenerationNames();
        } catch (Exception e) {
            log.error("获取所有辈分失败", e);
            throw new RuntimeException("获取辈分列表失败", e);
        }
    }
    
    @Override
    public Map<String, Object> getStatistics() {
        try {
            return familyMemberMapper.getStatistics();
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            throw new RuntimeException("获取统计信息失败", e);
        }
    }
    
    @Override
    public List<FamilyMember> searchMembersByName(String name) {
        try {
            return familyMemberMapper.findByNameLike(name);
        } catch (Exception e) {
            log.error("根据姓名搜索家族成员失败: {}", name, e);
            throw new RuntimeException("搜索家族成员失败", e);
        }
    }
    
    @Override
    @Transactional
    public boolean addChild(Long parentId, FamilyMember child) {
        try {
            FamilyMember parent = familyMemberMapper.findById(parentId);
            if (parent == null) {
                log.warn("父母不存在: {}", parentId);
                return false;
            }
            
            // 根据父母性别设置父亲或母亲ID
            if ("male".equals(parent.getGender())) {
                child.setFatherId(parentId);
            } else if ("female".equals(parent.getGender())) {
                child.setMotherId(parentId);
            }
            
            return addMember(child);
        } catch (Exception e) {
            log.error("添加子女失败: parentId={}, childName={}", parentId, child.getName(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean addSibling(Long siblingId, FamilyMember sibling) {
        try {
            FamilyMember existingSibling = familyMemberMapper.findById(siblingId);
            if (existingSibling == null) {
                log.warn("兄弟姐妹不存在: {}", siblingId);
                return false;
            }
            
            // 设置相同的父母
            sibling.setFatherId(existingSibling.getFatherId());
            sibling.setMotherId(existingSibling.getMotherId());
            sibling.setGenerationName(existingSibling.getGenerationName());
            
            return addMember(sibling);
        } catch (Exception e) {
            log.error("添加兄弟姐妹失败: siblingId={}, siblingName={}", siblingId, sibling.getName(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean addParent(Long childId, FamilyMember parent) {
        try {
            FamilyMember child = familyMemberMapper.findById(childId);
            if (child == null) {
                log.warn("子女不存在: {}", childId);
                return false;
            }
            
            // 先添加父母
            if (!addMember(parent)) {
                return false;
            }
            
            // 更新子女的父母信息
            if ("male".equals(parent.getGender())) {
                child.setFatherId(parent.getId());
            } else if ("female".equals(parent.getGender())) {
                child.setMotherId(parent.getId());
            }
            
            return updateMember(child);
        } catch (Exception e) {
            log.error("添加父母失败: childId={}, parentName={}", childId, parent.getName(), e);
            return false;
        }
    }
}
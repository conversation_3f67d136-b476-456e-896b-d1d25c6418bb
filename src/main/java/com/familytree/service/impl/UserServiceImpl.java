package com.familytree.service.impl;

import com.familytree.entity.User;
import com.familytree.mapper.UserMapper;
import com.familytree.service.UserService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public String login(String username, String password) {
        try {
            User user = userMapper.findByUsername(username);
            if (user == null) {
                log.warn("用户不存在: {}", username);
                return null;
            }
            
            if (!passwordEncoder.matches(password, user.getPassword())) {
                log.warn("密码错误: {}", username);
                return null;
            }
            
            String token = generateToken(user.getId());
            log.info("用户登录成功: {}", username);
            return token;
        } catch (Exception e) {
            log.error("用户登录失败: {}", username, e);
            return null;
        }
    }
    
    @Override
    public boolean register(String username, String password) {
        try {
            // 检查用户是否已存在
            User existingUser = userMapper.findByUsername(username);
            if (existingUser != null) {
                log.warn("用户已存在: {}", username);
                return false;
            }
            
            // 创建新用户
            User newUser = User.builder()
                    .username(username)
                    .password(passwordEncoder.encode(password))
                    .role("user")
                    .createdAt(LocalDateTime.now())
                    .build();
            
            int result = userMapper.insert(newUser);
            if (result > 0) {
                log.info("用户注册成功: {}", username);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("用户注册失败: {}", username, e);
            return false;
        }
    }
    
    @Override
    public User findByUsername(String username) {
        try {
            return userMapper.findByUsername(username);
        } catch (Exception e) {
            log.error("查找用户失败: {}", username, e);
            return null;
        }
    }
    
    @Override
    public User findById(Long id) {
        try {
            return userMapper.findById(id);
        } catch (Exception e) {
            log.error("查找用户失败: {}", id, e);
            return null;
        }
    }
    
    @Override
    public Long validateToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(jwtSecret)
                    .parseClaimsJws(token)
                    .getBody();
            
            return Long.valueOf(claims.getSubject());
        } catch (Exception e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            return null;
        }
    }
    
    @Override
    public String generateToken(Long userId) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration);
        
        return Jwts.builder()
                .setSubject(userId.toString())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }
}
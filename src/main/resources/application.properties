# Spring Boot Configuration
spring.application.name=family-tree-system
server.port=8080
server.servlet.context-path=/

# JSP Configuration
spring.mvc.view.prefix=/WEB-INF/views/
spring.mvc.view.suffix=.jsp

# Static Resources
spring.web.resources.static-locations=classpath:/static/,file:src/main/webapp/
spring.mvc.static-path-pattern=/static/**

# æ°æ®åºéç½® - MySQL
spring.datasource.url=*****************************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=root

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# MyBatis Configuration
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.familytree.entity
mybatis.configuration.map-underscore-to-camel-case=true

# Logging
logging.level.com.familytree=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JWT Configuration
jwt.secret=mySecretKey
jwt.expiration=86400000

# File Upload
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Actuator
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always

# Development Profile
spring.profiles.active=dev
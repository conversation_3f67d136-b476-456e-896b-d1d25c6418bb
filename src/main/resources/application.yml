server:
  port: 21826
  servlet:
    context-path: /

spring:
  application:
    name: family-tree-system
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: root
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JSP视图配置
  mvc:
    view:
      prefix: /WEB-INF/views/
      suffix: .jsp
    static-path-pattern: /static/**
  
  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/static/,classpath:/public/
  
  # 开发工具配置
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.familytree.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# JWT配置
jwt:
  secret: family_tree_secret_key
  expiration: 86400000  # 24小时

# 日志配置
logging:
  level:
    com.familytree: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
  file:
    name: logs/family-tree.log
    max-size: 10MB
    max-history: 30
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.familytree.mapper.FamilyMemberMapper">

    <!-- 结果映射 -->
    <resultMap id="FamilyMemberResultMap" type="com.familytree.entity.FamilyMember">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="birthDate" column="birth_date"/>
        <result property="deathDate" column="death_date"/>
        <result property="fatherId" column="father_id"/>
        <result property="motherId" column="mother_id"/>
        <result property="spouseId" column="spouse_id"/>
        <result property="gender" column="gender"/>
        <result property="photoUrl" column="photo_url"/>
        <result property="description" column="description"/>
        <result property="generationName" column="generation_name"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 查询所有家族成员 -->
    <select id="findAll" resultMap="FamilyMemberResultMap">
        SELECT id, name, birth_date, death_date, father_id, mother_id, spouse_id,
               gender, photo_url, description, generation_name, created_at, updated_at
        FROM family_members
        ORDER BY generation_name, name
    </select>

    <!-- 根据ID查找家族成员 -->
    <select id="findById" parameterType="long" resultMap="FamilyMemberResultMap">
        SELECT id, name, birth_date, death_date, father_id, mother_id, spouse_id,
               gender, photo_url, description, generation_name, created_at, updated_at
        FROM family_members
        WHERE id = #{id}
    </select>

    <!-- 根据辈分名称查询成员 -->
    <select id="findByGenerationName" parameterType="string" resultMap="FamilyMemberResultMap">
        SELECT id, name, birth_date, death_date, father_id, mother_id, spouse_id,
               gender, photo_url, description, generation_name, created_at, updated_at
        FROM family_members
        WHERE generation_name = #{generationName}
        ORDER BY name
    </select>

    <!-- 根据父亲ID查询子女 -->
    <select id="findByFatherId" parameterType="long" resultMap="FamilyMemberResultMap">
        SELECT id, name, birth_date, death_date, father_id, mother_id, spouse_id,
               gender, photo_url, description, generation_name, created_at, updated_at
        FROM family_members
        WHERE father_id = #{fatherId}
        ORDER BY birth_date
    </select>

    <!-- 根据母亲ID查询子女 -->
    <select id="findByMotherId" parameterType="long" resultMap="FamilyMemberResultMap">
        SELECT id, name, birth_date, death_date, father_id, mother_id, spouse_id,
               gender, photo_url, description, generation_name, created_at, updated_at
        FROM family_members
        WHERE mother_id = #{motherId}
        ORDER BY birth_date
    </select>

    <!-- 插入新成员 -->
    <insert id="insert" parameterType="com.familytree.entity.FamilyMember" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO family_members (
            name, birth_date, death_date, father_id, mother_id, spouse_id,
            gender, photo_url, description, generation_name, created_at, updated_at
        ) VALUES (
            #{name}, #{birthDate}, #{deathDate}, #{fatherId}, #{motherId}, #{spouseId},
            #{gender}, #{photoUrl}, #{description}, #{generationName}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 更新成员信息 -->
    <update id="update" parameterType="com.familytree.entity.FamilyMember">
        UPDATE family_members
        SET name = #{name},
            birth_date = #{birthDate},
            death_date = #{deathDate},
            father_id = #{fatherId},
            mother_id = #{motherId},
            spouse_id = #{spouseId},
            gender = #{gender},
            photo_url = #{photoUrl},
            description = #{description},
            generation_name = #{generationName},
            updated_at = #{updatedAt}
        WHERE id = #{id}
    </update>

    <!-- 删除成员 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM family_members WHERE id = #{id}
    </delete>

    <!-- 获取所有辈分名称 -->
    <select id="findAllGenerationNames" resultType="string">
        SELECT DISTINCT generation_name
        FROM family_members
        WHERE generation_name IS NOT NULL
        ORDER BY generation_name
    </select>

    <!-- 获取统计信息 -->
    <select id="getStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_members,
            COUNT(DISTINCT generation_name) as total_generations,
            SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_count,
            SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_count
        FROM family_members
    </select>

    <!-- 根据姓名模糊查询 -->
    <select id="findByNameLike" parameterType="string" resultMap="FamilyMemberResultMap">
        SELECT id, name, birth_date, death_date, father_id, mother_id, spouse_id,
               gender, photo_url, description, generation_name, created_at, updated_at
        FROM family_members
        WHERE name LIKE CONCAT('%', #{name}, '%')
        ORDER BY name
    </select>

</mapper>
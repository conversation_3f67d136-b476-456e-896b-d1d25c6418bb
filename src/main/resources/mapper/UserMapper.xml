<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.familytree.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="com.familytree.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="role" column="role"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>

    <!-- 根据用户名查找用户 -->
    <select id="findByUsername" parameterType="string" resultMap="UserResultMap">
        SELECT id, username, password, role, created_at
        FROM users
        WHERE username = #{username}
    </select>

    <!-- 根据ID查找用户 -->
    <select id="findById" parameterType="long" resultMap="UserResultMap">
        SELECT id, username, password, role, created_at
        FROM users
        WHERE id = #{id}
    </select>

    <!-- 插入新用户 -->
    <insert id="insert" parameterType="com.familytree.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, password, role, created_at)
        VALUES (#{username}, #{password}, #{role}, #{createdAt})
    </insert>

    <!-- 更新用户信息 -->
    <update id="update" parameterType="com.familytree.entity.User">
        UPDATE users
        SET username = #{username},
            password = #{password},
            role = #{role}
        WHERE id = #{id}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM users WHERE id = #{id}
    </delete>

</mapper>
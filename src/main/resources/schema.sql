-- 创建数据库
CREATE DATABASE IF NOT EXISTS family_tree DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE family_tree;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  role VARCHAR(20) DEFAULT 'user' COMMENT '角色',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建家族成员表
CREATE TABLE IF NOT EXISTS family_members (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(100) NOT NULL COMMENT '姓名',
  birth_date DATE COMMENT '出生日期',
  death_date DATE COMMENT '死亡日期',
  father_id BIGINT COMMENT '父亲ID',
  mother_id BIGINT COMMENT '母亲ID',
  spouse_id BIGINT COMMENT '配偶ID',
  gender VARCHAR(10) DEFAULT 'unknown' COMMENT '性别',
  photo_url VARCHAR(255) COMMENT '照片URL',
  description TEXT COMMENT '描述',
  generation_name VARCHAR(50) COMMENT '辈分名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_father_id (father_id),
  INDEX idx_mother_id (mother_id),
  INDEX idx_spouse_id (spouse_id),
  INDEX idx_generation_name (generation_name),
  INDEX idx_name (name),
  
  FOREIGN KEY (father_id) REFERENCES family_members(id) ON DELETE SET NULL,
  FOREIGN KEY (mother_id) REFERENCES family_members(id) ON DELETE SET NULL,
  FOREIGN KEY (spouse_id) REFERENCES family_members(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家族成员表';

-- 插入默认管理员用户
INSERT IGNORE INTO users (username, password, role) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'admin');
-- 默认密码: admin123

-- 插入示例家族成员数据
INSERT IGNORE INTO family_members (id, name, birth_date, gender, generation_name, description) VALUES 
(1, '张三', '1920-01-01', 'male', '第一代', '家族创始人'),
(2, '李四', '1922-03-15', 'female', '第一代', '张三的妻子'),
(3, '张大明', '1945-06-20', 'male', '第二代', '张三的长子'),
(4, '张小红', '1948-09-10', 'female', '第二代', '张三的女儿'),
(5, '张小强', '1970-12-05', 'male', '第三代', '张大明的儿子'),
(6, '张小美', '1972-04-18', 'female', '第三代', '张大明的女儿');

-- 更新家族关系
UPDATE family_members SET father_id = 1, mother_id = 2 WHERE id IN (3, 4);
UPDATE family_members SET father_id = 3 WHERE id IN (5, 6);
UPDATE family_members SET spouse_id = 2 WHERE id = 1;
UPDATE family_members SET spouse_id = 1 WHERE id = 2;
/* Tailwind CSS 核心样式 - 简化版本 */

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* 布局类 */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-16 { margin-bottom: 4rem; }
.mr-4 { margin-right: 1rem; }
.mr-2 { margin-right: 0.5rem; }
.mt-8 { margin-top: 2rem; }
.pt-8 { padding-top: 2rem; }

/* Flexbox */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }

/* Grid */
.grid { display: grid; }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.gap-8 { gap: 2rem; }

/* 响应式 */
@media (min-width: 768px) {
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* 隐藏/显示 */
.hidden { display: none; }
.block { display: block; }

/* 背景色 */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-800 { background-color: #1f2937; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }

/* 文字颜色 */
.text-white { color: #ffffff; }
.text-gray-400 { color: #9ca3af; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.text-green-600 { color: #059669; }
.text-purple-600 { color: #9333ea; }
.text-red-600 { color: #dc2626; }

/* 字体 */
.text-xl { font-size: 1.25rem; }
.text-lg { font-size: 1.125rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

/* 阴影 */
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* 圆角 */
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }

/* 定位 */
.sticky { position: sticky; }
.top-0 { top: 0; }
.z-50 { z-index: 50; }

/* 过渡效果 */
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }

/* 悬停效果 */
.hover\:text-blue-600:hover { color: #2563eb; }
.hover\:text-blue-800:hover { color: #1e40af; }
.hover\:text-white:hover { color: #ffffff; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-white:hover { background-color: #ffffff; }

/* 文本对齐 */
.text-center { text-align: center; }

/* 边框 */
.border-t { border-top-width: 1px; }
.border-2 { border-width: 2px; }
.border-white { border-color: #ffffff; }
.border-gray-700 { border-color: #374151; }

/* 自定义样式 */
.hero-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__delay-1s {
  animation-delay: 1s;
}

.animate__delay-2s {
  animation-delay: 2s;
}

/* 图标大小 */
.text-2xl { font-size: 1.5rem; }
.text-4xl { font-size: 2.25rem; }
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .family-tree {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .generation {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .member-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 200px;
            text-align: center;
        }
        
        .member-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            border-color: #3B82F6;
        }
        
        .member-card.male {
            border-left: 4px solid #3B82F6;
        }
        
        .member-card.female {
            border-left: 4px solid #EC4899;
        }
        
        .member-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 12px;
            background: #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #6B7280;
        }
        
        .member-photo img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .generation-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .connection-line {
            width: 2px;
            height: 30px;
            background: #D1D5DB;
            margin: 0 auto;
        }
        
        .search-container {
            position: sticky;
            top: 80px;
            z-index: 40;
            background: white;
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-sitemap text-2xl text-blue-600"></i>
                    <h1 class="text-xl font-bold text-gray-800">族谱管理系统</h1>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="/" class="text-gray-600 hover:text-blue-600 transition-colors">首页</a>
                    <a href="/family-tree" class="text-blue-600 hover:text-blue-800 font-medium">族谱图</a>
                    <a href="/member-management" class="text-gray-600 hover:text-blue-600 transition-colors">成员管理</a>
                    <a href="/generation-management" class="text-gray-600 hover:text-blue-600 transition-colors">辈分管理</a>
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                            <i class="fas fa-user"></i>
                            <span id="usernameDisplay">用户</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border">
                            <a href="#" id="logoutBtn" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                                <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                            </a>
                        </div>
                    </div>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 搜索和控制区域 -->
    <div class="search-container">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索家族成员..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <select id="generationFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">所有辈分</option>
                    </select>
                    <button id="refreshBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>刷新
                    </button>
                    <button id="expandAllBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>展开全部
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 族谱图容器 -->
    <div class="max-w-7xl mx-auto px-4 py-8">
        <div id="familyTreeContainer" class="family-tree">
            <!-- 加载中 -->
            <div id="loadingSpinner" class="flex items-center justify-center py-20">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <span class="ml-3 text-gray-600">加载族谱数据中...</span>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="hidden text-center py-20">
                <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无家族成员</h3>
                <p class="text-gray-500 mb-6">开始添加您的第一个家族成员</p>
                <a href="/member-management" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>添加成员
                </a>
            </div>
            
            <!-- 族谱内容 -->
            <div id="familyTreeContent" class="hidden w-full">
                <!-- 动态生成的族谱内容 -->
            </div>
        </div>
    </div>

    <!-- 成员详情模态框 -->
    <div id="memberModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">成员详情</h3>
                    <button id="closeMemberModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div id="memberDetails" class="space-y-4">
                    <!-- 动态生成的成员详情 -->
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button id="editMemberBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>编辑
                    </button>
                    <button id="closeMemberModal2" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let familyData = [];
        let filteredData = [];
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const username = localStorage.getItem('username');
            
            if (!token) {
                window.location.href = '/login';
                return false;
            }
            
            if (username) {
                document.getElementById('usernameDisplay').textContent = username;
            }
            
            return true;
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            window.location.href = '/login?logout=true';
        }
        
        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('token');
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }
        
        // 加载家族数据
        async function loadFamilyData() {
            try {
                const response = await fetch('/api/family-members', {
                    headers: getAuthHeaders()
                });
                
                if (response.status === 401) {
                    logout();
                    return;
                }
                
                if (response.ok) {
                    familyData = await response.json();
                    filteredData = [...familyData];
                    renderFamilyTree();
                    loadGenerationFilter();
                } else {
                    throw new Error('加载数据失败');
                }
            } catch (error) {
                console.error('加载家族数据失败:', error);
                showError('加载家族数据失败，请刷新页面重试');
            } finally {
                document.getElementById('loadingSpinner').classList.add('hidden');
            }
        }
        
        // 加载辈分筛选选项
        async function loadGenerationFilter() {
            try {
                const response = await fetch('/api/family-members/generations', {
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const generations = await response.json();
                    const select = document.getElementById('generationFilter');
                    
                    // 清空现有选项（保留"所有辈分"）
                    select.innerHTML = '<option value="">所有辈分</option>';
                    
                    generations.forEach(generation => {
                        const option = document.createElement('option');
                        option.value = generation;
                        option.textContent = generation;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载辈分数据失败:', error);
            }
        }
        
        // 渲染族谱图
        function renderFamilyTree() {
            const container = document.getElementById('familyTreeContent');
            const emptyState = document.getElementById('emptyState');
            
            if (filteredData.length === 0) {
                container.classList.add('hidden');
                emptyState.classList.remove('hidden');
                return;
            }
            
            emptyState.classList.add('hidden');
            container.classList.remove('hidden');
            
            // 按辈分分组
            const generationGroups = {};
            filteredData.forEach(member => {
                const generation = member.generationName || '未分配辈分';
                if (!generationGroups[generation]) {
                    generationGroups[generation] = [];
                }
                generationGroups[generation].push(member);
            });
            
            // 生成HTML
            let html = '';
            const sortedGenerations = Object.keys(generationGroups).sort();
            
            sortedGenerations.forEach((generation, index) => {
                if (index > 0) {
                    html += '<div class="connection-line"></div>';
                }
                
                html += `
                    <div class="generation">
                        <div class="w-full text-center mb-4">
                            <span class="generation-title">${generation}</span>
                        </div>
                    </div>
                    <div class="generation">
                `;
                
                generationGroups[generation].forEach(member => {
                    html += createMemberCard(member);
                });
                
                html += '</div>';
            });
            
            container.innerHTML = html;
            
            // 添加点击事件
            container.querySelectorAll('.member-card').forEach(card => {
                card.addEventListener('click', () => {
                    const memberId = card.dataset.memberId;
                    showMemberDetails(memberId);
                });
            });
        }
        
        // 创建成员卡片
        function createMemberCard(member) {
            const genderClass = member.gender === 'male' ? 'male' : member.gender === 'female' ? 'female' : '';
            const genderIcon = member.gender === 'male' ? 'fa-mars' : member.gender === 'female' ? 'fa-venus' : 'fa-question';
            const photoHtml = member.photoUrl ? 
                `<img src="${member.photoUrl}" alt="${member.name}">` : 
                `<i class="fas ${genderIcon}"></i>`;
            
            const birthYear = member.birthDate ? new Date(member.birthDate).getFullYear() : '';
            const deathYear = member.deathDate ? new Date(member.deathDate).getFullYear() : '';
            const lifeSpan = birthYear ? (deathYear ? `${birthYear}-${deathYear}` : `${birthYear}-`) : '';
            
            return `
                <div class="member-card ${genderClass}" data-member-id="${member.id}">
                    <div class="member-photo">
                        ${photoHtml}
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-1">${member.name}</h4>
                    ${lifeSpan ? `<p class="text-sm text-gray-600 mb-2">${lifeSpan}</p>` : ''}
                    ${member.description ? `<p class="text-xs text-gray-500 truncate">${member.description}</p>` : ''}
                </div>
            `;
        }
        
        // 显示成员详情
        function showMemberDetails(memberId) {
            const member = familyData.find(m => m.id == memberId);
            if (!member) return;
            
            const modal = document.getElementById('memberModal');
            const details = document.getElementById('memberDetails');
            
            const genderText = member.gender === 'male' ? '男' : member.gender === 'female' ? '女' : '未知';
            const birthDate = member.birthDate ? new Date(member.birthDate).toLocaleDateString('zh-CN') : '未知';
            const deathDate = member.deathDate ? new Date(member.deathDate).toLocaleDateString('zh-CN') : '';
            
            // 查找关系
            const father = member.fatherId ? familyData.find(m => m.id === member.fatherId) : null;
            const mother = member.motherId ? familyData.find(m => m.id === member.motherId) : null;
            const spouse = member.spouseId ? familyData.find(m => m.id === member.spouseId) : null;
            const children = familyData.filter(m => m.fatherId === member.id || m.motherId === member.id);
            
            details.innerHTML = `
                <div class="text-center mb-6">
                    <div class="w-20 h-20 mx-auto mb-3 rounded-full bg-gray-100 flex items-center justify-center">
                        ${member.photoUrl ? 
                            `<img src="${member.photoUrl}" alt="${member.name}" class="w-full h-full rounded-full object-cover">` :
                            `<i class="fas ${member.gender === 'male' ? 'fa-mars' : member.gender === 'female' ? 'fa-venus' : 'fa-user'} text-2xl text-gray-400"></i>`
                        }
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">${member.name}</h3>
                </div>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">性别:</span>
                        <span class="font-medium">${genderText}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">出生日期:</span>
                        <span class="font-medium">${birthDate}</span>
                    </div>
                    ${deathDate ? `
                        <div class="flex justify-between">
                            <span class="text-gray-600">死亡日期:</span>
                            <span class="font-medium">${deathDate}</span>
                        </div>
                    ` : ''}
                    <div class="flex justify-between">
                        <span class="text-gray-600">辈分:</span>
                        <span class="font-medium">${member.generationName || '未分配'}</span>
                    </div>
                    ${father ? `
                        <div class="flex justify-between">
                            <span class="text-gray-600">父亲:</span>
                            <span class="font-medium text-blue-600 cursor-pointer" onclick="showMemberDetails(${father.id})">${father.name}</span>
                        </div>
                    ` : ''}
                    ${mother ? `
                        <div class="flex justify-between">
                            <span class="text-gray-600">母亲:</span>
                            <span class="font-medium text-blue-600 cursor-pointer" onclick="showMemberDetails(${mother.id})">${mother.name}</span>
                        </div>
                    ` : ''}
                    ${spouse ? `
                        <div class="flex justify-between">
                            <span class="text-gray-600">配偶:</span>
                            <span class="font-medium text-blue-600 cursor-pointer" onclick="showMemberDetails(${spouse.id})">${spouse.name}</span>
                        </div>
                    ` : ''}
                    ${children.length > 0 ? `
                        <div>
                            <span class="text-gray-600">子女:</span>
                            <div class="mt-2 space-y-1">
                                ${children.map(child => 
                                    `<span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm cursor-pointer" onclick="showMemberDetails(${child.id})">${child.name}</span>`
                                ).join(' ')}
                            </div>
                        </div>
                    ` : ''}
                    ${member.description ? `
                        <div>
                            <span class="text-gray-600">描述:</span>
                            <p class="mt-1 text-gray-800">${member.description}</p>
                        </div>
                    ` : ''}
                </div>
            `;
            
            // 设置编辑按钮
            document.getElementById('editMemberBtn').onclick = () => {
                window.location.href = `/member-management?edit=${member.id}`;
            };
            
            modal.classList.remove('hidden');
        }
        
        // 搜索功能
        function filterMembers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const selectedGeneration = document.getElementById('generationFilter').value;
            
            filteredData = familyData.filter(member => {
                const matchesSearch = !searchTerm || 
                    member.name.toLowerCase().includes(searchTerm) ||
                    (member.description && member.description.toLowerCase().includes(searchTerm));
                
                const matchesGeneration = !selectedGeneration || member.generationName === selectedGeneration;
                
                return matchesSearch && matchesGeneration;
            });
            
            renderFamilyTree();
        }
        
        // 显示错误信息
        function showError(message) {
            // 简单的错误提示，可以根据需要改进
            alert(message);
        }
        
        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkAuth()) return;
            
            // 加载数据
            loadFamilyData();
            
            // 搜索和筛选
            document.getElementById('searchInput').addEventListener('input', filterMembers);
            document.getElementById('generationFilter').addEventListener('change', filterMembers);
            
            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', () => {
                document.getElementById('loadingSpinner').classList.remove('hidden');
                document.getElementById('familyTreeContent').classList.add('hidden');
                loadFamilyData();
            });
            
            // 展开全部按钮（暂时只是刷新）
            document.getElementById('expandAllBtn').addEventListener('click', () => {
                document.getElementById('searchInput').value = '';
                document.getElementById('generationFilter').value = '';
                filterMembers();
            });
            
            // 用户菜单
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                document.getElementById('userMenu').classList.toggle('hidden');
            });
            
            document.addEventListener('click', function() {
                document.getElementById('userMenu').classList.add('hidden');
            });
            
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
            
            // 模态框关闭
            document.getElementById('closeMemberModal').addEventListener('click', () => {
                document.getElementById('memberModal').classList.add('hidden');
            });
            
            document.getElementById('closeMemberModal2').addEventListener('click', () => {
                document.getElementById('memberModal').classList.add('hidden');
            });
            
            document.getElementById('memberModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                }
            });
            
            // 移动端菜单
            document.getElementById('mobile-menu-btn').addEventListener('click', function() {
                // 移动端菜单逻辑（可以根据需要实现）
            });
        });
    </script>
</body>
</html>
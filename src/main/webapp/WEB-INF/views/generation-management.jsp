<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .generation-card {
            transition: all 0.3s ease;
        }
        .generation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-sitemap text-2xl text-blue-600"></i>
                    <h1 class="text-xl font-bold text-gray-800">族谱管理系统</h1>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="/" class="text-gray-600 hover:text-blue-600 transition-colors">首页</a>
                    <a href="/family-tree" class="text-gray-600 hover:text-blue-600 transition-colors">族谱图</a>
                    <a href="/member-management" class="text-gray-600 hover:text-blue-600 transition-colors">成员管理</a>
                    <a href="/generation-management" class="text-blue-600 hover:text-blue-800 font-medium">辈分管理</a>
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                            <i class="fas fa-user"></i>
                            <span id="usernameDisplay">用户</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border">
                            <a href="#" id="logoutBtn" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                                <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 页面标题和操作 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">辈分管理</h1>
                <p class="text-gray-600">管理家族辈分信息和成员分布</p>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-layer-group text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总辈分数</p>
                        <p id="totalGenerations" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总成员数</p>
                        <p id="totalMembers" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-crown text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">最大辈分</p>
                        <p id="maxGeneration" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="fas fa-chart-bar text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">平均每辈</p>
                        <p id="avgMembersPerGeneration" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 辈分列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <!-- 加载中 -->
            <div id="loadingSpinner" class="flex items-center justify-center py-20">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <span class="ml-3 text-gray-600">加载辈分数据中...</span>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="hidden text-center py-20">
                <i class="fas fa-layer-group text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无辈分信息</h3>
                <p class="text-gray-500 mb-6">请先添加家族成员并设置辈分</p>
                <a href="/member-management" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block">
                    <i class="fas fa-plus mr-2"></i>添加成员
                </a>
            </div>
            
            <!-- 辈分表格 -->
            <div id="generationsTable" class="hidden">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">辈分详情</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">辈分名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成员数量</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">男性</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">女性</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="generationsTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 动态生成的辈分行 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 辈分成员详情 -->
        <div id="generationMembersSection" class="hidden mt-8">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="generationMembersTitle" class="text-lg font-semibold text-gray-800">辈分成员</h3>
                    <button id="closeGenerationMembers" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div id="generationMembersGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <!-- 动态生成的成员卡片 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 成员详情模态框 -->
    <div id="memberDetailModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">成员详情</h3>
                    <button id="closeMemberDetailModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div id="memberDetailContent">
                    <!-- 动态生成的成员详情 -->
                </div>
                
                <div class="flex justify-end space-x-3 pt-6 border-t">
                    <button id="editMemberBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>编辑
                    </button>
                    <button id="closeMemberDetailBtn" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示消息 -->
    <div id="toast" class="hidden fixed top-4 right-4 z-50 max-w-sm">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4">
            <div class="flex items-center">
                <div id="toastIcon" class="flex-shrink-0 mr-3">
                    <!-- 动态图标 -->
                </div>
                <div id="toastMessage" class="text-sm font-medium text-gray-900">
                    <!-- 动态消息 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let generationsData = [];
        let membersData = [];
        let currentGenerationMembers = [];
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const username = localStorage.getItem('username');
            
            if (!token) {
                window.location.href = '/login';
                return false;
            }
            
            if (username) {
                document.getElementById('usernameDisplay').textContent = username;
            }
            
            return true;
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            window.location.href = '/login?logout=true';
        }
        
        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('token');
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }
        
        // 显示提示消息
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const icon = document.getElementById('toastIcon');
            const messageEl = document.getElementById('toastMessage');
            
            const icons = {
                success: '<i class="fas fa-check-circle text-green-500"></i>',
                error: '<i class="fas fa-exclamation-circle text-red-500"></i>',
                warning: '<i class="fas fa-exclamation-triangle text-yellow-500"></i>',
                info: '<i class="fas fa-info-circle text-blue-500"></i>'
            };
            
            icon.innerHTML = icons[type] || icons.info;
            messageEl.textContent = message;
            
            toast.classList.remove('hidden');
            toast.classList.add('animate__animated', 'animate__fadeInRight');
            
            setTimeout(() => {
                toast.classList.add('animate__fadeOutRight');
                setTimeout(() => {
                    toast.classList.add('hidden');
                    toast.classList.remove('animate__animated', 'animate__fadeInRight', 'animate__fadeOutRight');
                }, 300);
            }, 3000);
        }
        
        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await fetch('/api/family-members/statistics', {
                    headers: getAuthHeaders()
                });
                
                if (response.status === 401) {
                    logout();
                    return;
                }
                
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('totalMembers').textContent = stats.totalMembers || 0;
                } else {
                    throw new Error('加载统计数据失败');
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }
        
        // 加载辈分数据
        async function loadGenerations() {
            try {
                const response = await fetch('/api/family-members/generations', {
                    headers: getAuthHeaders()
                });
                
                if (response.status === 401) {
                    logout();
                    return;
                }
                
                if (response.ok) {
                    const generations = await response.json();
                    await loadGenerationDetails(generations);
                } else {
                    throw new Error('加载辈分数据失败');
                }
            } catch (error) {
                console.error('加载辈分数据失败:', error);
                showToast('加载辈分数据失败，请刷新页面重试', 'error');
            } finally {
                document.getElementById('loadingSpinner').classList.add('hidden');
            }
        }
        
        // 加载辈分详情
        async function loadGenerationDetails(generations) {
            if (generations.length === 0) {
                document.getElementById('emptyState').classList.remove('hidden');
                return;
            }
            
            try {
                // 加载所有成员数据
                const membersResponse = await fetch('/api/family-members', {
                    headers: getAuthHeaders()
                });
                
                if (membersResponse.ok) {
                    membersData = await membersResponse.json();
                    
                    // 计算每个辈分的统计信息
                    generationsData = generations.map(generation => {
                        const members = membersData.filter(member => member.generationName === generation);
                        const maleCount = members.filter(member => member.gender === 'male').length;
                        const femaleCount = members.filter(member => member.gender === 'female').length;
                        
                        return {
                            name: generation,
                            totalCount: members.length,
                            maleCount: maleCount,
                            femaleCount: femaleCount,
                            percentage: membersData.length > 0 ? ((members.length / membersData.length) * 100).toFixed(1) : 0,
                            members: members
                        };
                    });
                    
                    // 更新统计信息
                    updateStatistics();
                    
                    // 渲染辈分表格
                    renderGenerationsTable();
                } else {
                    throw new Error('加载成员数据失败');
                }
            } catch (error) {
                console.error('加载辈分详情失败:', error);
                showToast('加载辈分详情失败', 'error');
            }
        }
        
        // 更新统计信息
        function updateStatistics() {
            document.getElementById('totalGenerations').textContent = generationsData.length;
            document.getElementById('totalMembers').textContent = membersData.length;
            
            if (generationsData.length > 0) {
                const maxGeneration = generationsData.reduce((max, gen) => 
                    gen.totalCount > max.totalCount ? gen : max
                );
                document.getElementById('maxGeneration').textContent = maxGeneration.name;
                
                const avgMembers = (membersData.length / generationsData.length).toFixed(1);
                document.getElementById('avgMembersPerGeneration').textContent = avgMembers;
            } else {
                document.getElementById('maxGeneration').textContent = '-';
                document.getElementById('avgMembersPerGeneration').textContent = '-';
            }
        }
        
        // 渲染辈分表格
        function renderGenerationsTable() {
            const tbody = document.getElementById('generationsTableBody');
            const table = document.getElementById('generationsTable');
            
            if (generationsData.length === 0) {
                document.getElementById('emptyState').classList.remove('hidden');
                return;
            }
            
            table.classList.remove('hidden');
            
            tbody.innerHTML = generationsData.map(generation => {
                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-layer-group text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">${generation.name}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${generation.totalCount}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <i class="fas fa-mars text-blue-500 mr-1"></i>
                                ${generation.maleCount}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <i class="fas fa-venus text-pink-500 mr-1"></i>
                                ${generation.femaleCount}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="text-sm text-gray-900">${generation.percentage}%</div>
                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: ${generation.percentage}%"></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="showGenerationMembers('${generation.name}')" class="text-blue-600 hover:text-blue-900 mr-3">
                                <i class="fas fa-eye mr-1"></i>查看成员
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        // 显示辈分成员
        function showGenerationMembers(generationName) {
            const generation = generationsData.find(g => g.name === generationName);
            if (!generation) return;
            
            currentGenerationMembers = generation.members;
            document.getElementById('generationMembersTitle').textContent = `${generationName} - ${generation.totalCount}位成员`;
            
            const grid = document.getElementById('generationMembersGrid');
            grid.innerHTML = currentGenerationMembers.map(member => createMemberCard(member)).join('');
            
            document.getElementById('generationMembersSection').classList.remove('hidden');
            
            // 滚动到成员区域
            document.getElementById('generationMembersSection').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }
        
        // 创建成员卡片
        function createMemberCard(member) {
            const genderIcon = member.gender === 'male' ? 'fa-mars text-blue-500' : 
                              member.gender === 'female' ? 'fa-venus text-pink-500' : 'fa-question text-gray-500';
            const genderText = member.gender === 'male' ? '男' : member.gender === 'female' ? '女' : '未知';
            
            const birthYear = member.birthDate ? new Date(member.birthDate).getFullYear() : '';
            const deathYear = member.deathDate ? new Date(member.deathDate).getFullYear() : '';
            const lifeSpan = birthYear ? (deathYear ? `${birthYear}-${deathYear}` : `${birthYear}-`) : '';
            
            return `
                <div class="generation-card bg-gray-50 rounded-lg p-4 cursor-pointer hover:bg-gray-100" onclick="showMemberDetail(${member.id})">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                            ${member.photoUrl ? 
                                `<img src="${member.photoUrl}" alt="${member.name}" class="w-full h-full rounded-full object-cover">` :
                                `<i class="fas ${genderIcon}"></i>`
                            }
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800">${member.name}</h4>
                            <p class="text-xs text-gray-500">${genderText}</p>
                        </div>
                    </div>
                    
                    ${lifeSpan ? `<p class="text-xs text-gray-600 mb-2"><i class="fas fa-calendar mr-1"></i>${lifeSpan}</p>` : ''}
                    ${member.description ? `<p class="text-xs text-gray-600 truncate">${member.description}</p>` : ''}
                </div>
            `;
        }
        
        // 显示成员详情
        function showMemberDetail(memberId) {
            const member = membersData.find(m => m.id === memberId);
            if (!member) return;
            
            const genderIcon = member.gender === 'male' ? 'fa-mars text-blue-500' : 
                              member.gender === 'female' ? 'fa-venus text-pink-500' : 'fa-question text-gray-500';
            const genderText = member.gender === 'male' ? '男' : member.gender === 'female' ? '女' : '未知';
            
            const birthDate = member.birthDate ? new Date(member.birthDate).toLocaleDateString('zh-CN') : '未知';
            const deathDate = member.deathDate ? new Date(member.deathDate).toLocaleDateString('zh-CN') : '';
            
            // 查找家庭关系
            const father = member.fatherId ? membersData.find(m => m.id === member.fatherId) : null;
            const mother = member.motherId ? membersData.find(m => m.id === member.motherId) : null;
            const spouse = member.spouseId ? membersData.find(m => m.id === member.spouseId) : null;
            
            const content = `
                <div class="text-center mb-6">
                    <div class="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4">
                        ${member.photoUrl ? 
                            `<img src="${member.photoUrl}" alt="${member.name}" class="w-full h-full rounded-full object-cover">` :
                            `<i class="fas ${genderIcon} text-2xl"></i>`
                        }
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">${member.name}</h3>
                    <p class="text-gray-600">${genderText}</p>
                </div>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">出生日期:</span>
                            <p class="font-medium">${birthDate}</p>
                        </div>
                        ${deathDate ? `
                        <div>
                            <span class="text-gray-500">死亡日期:</span>
                            <p class="font-medium">${deathDate}</p>
                        </div>
                        ` : ''}
                        <div>
                            <span class="text-gray-500">辈分:</span>
                            <p class="font-medium">${member.generationName || '未设置'}</p>
                        </div>
                        <div>
                            <span class="text-gray-500">ID:</span>
                            <p class="font-medium">${member.id}</p>
                        </div>
                    </div>
                    
                    ${member.description ? `
                    <div>
                        <span class="text-gray-500 text-sm">描述:</span>
                        <p class="mt-1 text-gray-800">${member.description}</p>
                    </div>
                    ` : ''}
                    
                    <div>
                        <span class="text-gray-500 text-sm">家庭关系:</span>
                        <div class="mt-2 space-y-2">
                            ${father ? `<p class="text-sm"><i class="fas fa-male text-blue-500 mr-2"></i>父亲: ${father.name}</p>` : ''}
                            ${mother ? `<p class="text-sm"><i class="fas fa-female text-pink-500 mr-2"></i>母亲: ${mother.name}</p>` : ''}
                            ${spouse ? `<p class="text-sm"><i class="fas fa-heart text-red-500 mr-2"></i>配偶: ${spouse.name}</p>` : ''}
                            ${!father && !mother && !spouse ? '<p class="text-sm text-gray-500">暂无家庭关系信息</p>' : ''}
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('memberDetailContent').innerHTML = content;
            document.getElementById('editMemberBtn').onclick = () => {
                window.location.href = `/member-management?edit=${member.id}`;
            };
            document.getElementById('memberDetailModal').classList.remove('hidden');
        }
        
        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkAuth()) return;
            
            // 加载数据
            loadStatistics();
            loadGenerations();
            
            // 关闭辈分成员区域
            document.getElementById('closeGenerationMembers').addEventListener('click', () => {
                document.getElementById('generationMembersSection').classList.add('hidden');
            });
            
            // 关闭成员详情模态框
            document.getElementById('closeMemberDetailModal').addEventListener('click', () => {
                document.getElementById('memberDetailModal').classList.add('hidden');
            });
            
            document.getElementById('closeMemberDetailBtn').addEventListener('click', () => {
                document.getElementById('memberDetailModal').classList.add('hidden');
            });
            
            // 用户菜单
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                document.getElementById('userMenu').classList.toggle('hidden');
            });
            
            document.addEventListener('click', function() {
                document.getElementById('userMenu').classList.add('hidden');
            });
            
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        });
        
        // 全局函数
        window.showGenerationMembers = showGenerationMembers;
        window.showMemberDetail = showMemberDetail;
    </script>
</body>
</html>
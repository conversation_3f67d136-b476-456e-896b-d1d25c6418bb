<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-sitemap text-2xl text-blue-600"></i>
                    <h1 class="text-xl font-bold text-gray-800">族谱管理系统</h1>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="/" class="text-blue-600 hover:text-blue-800 font-medium">首页</a>
                    <a href="/family-tree" class="text-gray-600 hover:text-blue-600 transition-colors">族谱图</a>
                    <a href="/member-management" class="text-gray-600 hover:text-blue-600 transition-colors">成员管理</a>
                    <a href="/generation-management" class="text-gray-600 hover:text-blue-600 transition-colors">辈分管理</a>
                    <div id="authSection">
                        <a href="/login" id="loginBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">登录</a>
                        <div id="userSection" class="hidden flex items-center space-x-4">
                            <span class="text-gray-600">欢迎，<span id="usernameDisplay"></span></span>
                            <button onclick="logout()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">退出</button>
                        </div>
                    </div>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-600 hover:text-blue-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="/" class="block py-2 text-blue-600 font-medium">首页</a>
                <a href="/family-tree" class="block py-2 text-gray-600 hover:text-blue-600">族谱图</a>
                <a href="/member-management" class="block py-2 text-gray-600 hover:text-blue-600">成员管理</a>
                <a href="/generation-management" class="block py-2 text-gray-600 hover:text-blue-600">辈分管理</a>
                <a href="/login" class="block py-2 text-gray-600 hover:text-blue-600">登录</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero Section -->
        <section class="hero-bg text-white py-20">
            <div class="max-w-7xl mx-auto px-4 text-center">
                <h1 class="text-5xl font-bold mb-6 animate__animated animate__fadeInUp">族谱管理系统</h1>
                <p class="text-xl mb-8 animate__animated animate__fadeInUp animate__delay-1s">传承家族文化，记录血脉传承</p>
                <div class="animate__animated animate__fadeInUp animate__delay-2s">
                    <a href="/family-tree" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors mr-4">
                        查看族谱
                    </a>
                    <a href="/member-management" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        管理成员
                    </a>
                </div>
            </div>
        </section>

        <!-- 功能特色 -->
        <section class="py-20">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">系统功能</h2>
                    <p class="text-gray-600 text-lg">全面的族谱管理解决方案</p>
                </div>
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                        <div class="text-blue-600 text-4xl mb-4">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">族谱图谱</h3>
                        <p class="text-gray-600">直观展示家族关系，清晰的世代层级结构</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                        <div class="text-green-600 text-4xl mb-4">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">成员管理</h3>
                        <p class="text-gray-600">添加、编辑、删除家族成员信息</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                        <div class="text-purple-600 text-4xl mb-4">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">辈分管理</h3>
                        <p class="text-gray-600">管理家族辈分，维护传统文化</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                        <div class="text-red-600 text-4xl mb-4">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">智能搜索</h3>
                        <p class="text-gray-600">快速查找家族成员，支持多种搜索方式</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计信息 -->
        <section class="bg-gray-100 py-20">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">家族统计</h2>
                    <p class="text-gray-600 text-lg">了解您的家族规模</p>
                </div>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white p-8 rounded-xl shadow-lg text-center">
                        <div class="text-4xl font-bold text-blue-600 mb-2" id="total-members">0</div>
                        <div class="text-gray-600">家族成员总数</div>
                    </div>
                    <div class="bg-white p-8 rounded-xl shadow-lg text-center">
                        <div class="text-4xl font-bold text-green-600 mb-2" id="total-generations">0</div>
                        <div class="text-gray-600">世代层数</div>
                    </div>
                    <div class="bg-white p-8 rounded-xl shadow-lg text-center">
                        <div class="text-4xl font-bold text-purple-600 mb-2" id="male-members">0</div>
                        <div class="text-gray-600">男性成员</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">族谱管理系统</h3>
                    <p class="text-gray-400">传承家族文化，记录血脉传承，让家族历史永远流传。</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/family-tree" class="hover:text-white transition-colors">族谱图</a></li>
                        <li><a href="/member-management" class="hover:text-white transition-colors">成员管理</a></li>
                        <li><a href="/generation-management" class="hover:text-white transition-colors">辈分管理</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <p class="text-gray-400">如有问题或建议，请联系系统管理员。</p>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 族谱管理系统. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const username = localStorage.getItem('username');
            
            if (token && username) {
                // 用户已登录
                document.getElementById('loginBtn').classList.add('hidden');
                document.getElementById('userSection').classList.remove('hidden');
                document.getElementById('usernameDisplay').textContent = username;
                return true;
            } else {
                // 用户未登录
                document.getElementById('loginBtn').classList.remove('hidden');
                document.getElementById('userSection').classList.add('hidden');
                return false;
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            window.location.href = '/login?logout=true';
        }
        
        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('token');
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }

        // 移动端菜单切换
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // 加载统计数据
        async function loadStatistics() {
            try {
                const headers = checkAuth() ? getAuthHeaders() : { 'Content-Type': 'application/json' };
                const response = await fetch('/api/stats', { headers });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        const stats = data.data;
                        document.getElementById('total-members').textContent = stats.totalMembers || 0;
                        document.getElementById('total-generations').textContent = stats.totalGenerations || 0;
                        document.getElementById('male-members').textContent = stats.maleMembers || 0;
                    }
                } else if (response.status === 401) {
                    // 未认证，清除本地存储并显示登录按钮
                    localStorage.removeItem('token');
                    localStorage.removeItem('username');
                    checkAuth();
                    console.log('需要登录才能查看统计数据');
                } else {
                    console.log('统计数据API暂不可用');
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadStatistics();
        });
    </script>
</body>
</html>
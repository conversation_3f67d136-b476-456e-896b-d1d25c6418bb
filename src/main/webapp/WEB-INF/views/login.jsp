<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- 登录卡片 -->
        <div class="bg-white rounded-2xl shadow-2xl p-8 animate__animated animate__fadeInUp">
            <!-- 头部 -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <i class="fas fa-sitemap text-2xl text-blue-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">欢迎回来</h1>
                <p class="text-gray-600">登录到族谱管理系统</p>
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2"></i>用户名
                    </label>
                    <input type="text" id="username" name="username" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                           placeholder="请输入用户名">
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2"></i>密码
                    </label>
                    <div class="relative">
                        <input type="password" id="password" name="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all pr-12"
                               placeholder="请输入密码">
                        <button type="button" id="togglePassword" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-600">记住我</span>
                    </label>
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘记密码？</a>
                </div>

                <button type="submit" id="loginBtn"
                        class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all">
                    <span id="loginBtnText">登录</span>
                    <i id="loginSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                </button>
            </form>

            <!-- 分割线 -->
            <div class="mt-8 text-center">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">或者</span>
                    </div>
                </div>
            </div>

            <!-- 注册链接 -->
            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    还没有账户？
                    <a href="/register" class="text-blue-600 hover:text-blue-800 font-medium">立即注册</a>
                </p>
            </div>

            <!-- 返回首页 -->
            <div class="mt-4 text-center">
                <a href="/" class="text-gray-500 hover:text-gray-700 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>返回首页
                </a>
            </div>
        </div>

        <!-- 错误提示 -->
        <div id="errorAlert" class="hidden mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg animate__animated animate__fadeInDown">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span id="errorMessage"></span>
            </div>
        </div>

        <!-- 成功提示 -->
        <div id="successAlert" class="hidden mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg animate__animated animate__fadeInDown">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span id="successMessage"></span>
            </div>
        </div>
    </div>

    <script>
        // 密码显示/隐藏切换
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // 显示错误信息
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            const successAlert = document.getElementById('successAlert');
            
            successAlert.classList.add('hidden');
            errorMessage.textContent = message;
            errorAlert.classList.remove('hidden');
            
            setTimeout(() => {
                errorAlert.classList.add('hidden');
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            const successAlert = document.getElementById('successAlert');
            const successMessage = document.getElementById('successMessage');
            const errorAlert = document.getElementById('errorAlert');
            
            errorAlert.classList.add('hidden');
            successMessage.textContent = message;
            successAlert.classList.remove('hidden');
            
            setTimeout(() => {
                successAlert.classList.add('hidden');
            }, 3000);
        }

        // 设置加载状态
        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginSpinner = document.getElementById('loginSpinner');
            
            if (loading) {
                loginBtn.disabled = true;
                loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
                loginBtnText.textContent = '登录中...';
                loginSpinner.classList.remove('hidden');
            } else {
                loginBtn.disabled = false;
                loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                loginBtnText.textContent = '登录';
                loginSpinner.classList.add('hidden');
            }
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showError('请输入用户名和密码');
                return;
            }
            
            setLoading(true);
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 保存token
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('username', data.username);
                    
                    showSuccess('登录成功，正在跳转...');
                    
                    // 跳转到首页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showError(data.message || '登录失败，请检查用户名和密码');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showError('网络错误，请稍后重试');
            } finally {
                setLoading(false);
            }
        });

        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('logout')) {
            showSuccess('已成功退出登录');
        }
        if (urlParams.get('registered')) {
            showSuccess('注册成功，请登录');
        }
    </script>
</body>
</html>
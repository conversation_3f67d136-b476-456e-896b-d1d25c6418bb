<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .member-card {
            transition: all 0.3s ease;
        }
        .member-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-sitemap text-2xl text-blue-600"></i>
                    <h1 class="text-xl font-bold text-gray-800">族谱管理系统</h1>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="/" class="text-gray-600 hover:text-blue-600 transition-colors">首页</a>
                    <a href="/family-tree" class="text-gray-600 hover:text-blue-600 transition-colors">族谱图</a>
                    <a href="/member-management" class="text-blue-600 hover:text-blue-800 font-medium">成员管理</a>
                    <a href="/generation-management" class="text-gray-600 hover:text-blue-600 transition-colors">辈分管理</a>
                    <div class="relative">
                        <button id="userMenuBtn" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                            <i class="fas fa-user"></i>
                            <span id="usernameDisplay">用户</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border">
                            <a href="#" id="logoutBtn" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                                <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 页面标题和操作 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">成员管理</h1>
                <p class="text-gray-600">管理家族成员信息</p>
            </div>
            <div class="mt-4 md:mt-0">
                <button id="addMemberBtn" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>添加成员
                </button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索成员</label>
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="输入姓名搜索..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">辈分筛选</label>
                    <select id="generationFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">所有辈分</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">性别筛选</label>
                    <select id="genderFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">所有性别</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                        <option value="unknown">未知</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button id="resetFiltersBtn" class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-undo mr-2"></i>重置筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 成员列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <!-- 加载中 -->
            <div id="loadingSpinner" class="flex items-center justify-center py-20">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <span class="ml-3 text-gray-600">加载成员数据中...</span>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="hidden text-center py-20">
                <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无家族成员</h3>
                <p class="text-gray-500 mb-6">开始添加您的第一个家族成员</p>
                <button id="addFirstMemberBtn" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>添加成员
                </button>
            </div>
            
            <!-- 成员网格 -->
            <div id="membersGrid" class="hidden p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="membersContainer">
                    <!-- 动态生成的成员卡片 -->
                </div>
                
                <!-- 分页 -->
                <div id="pagination" class="mt-8 flex justify-center">
                    <!-- 分页控件 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑成员模态框 -->
    <div id="memberModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 id="modalTitle" class="text-xl font-bold text-gray-800">添加成员</h3>
                    <button id="closeMemberModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <form id="memberForm" class="space-y-6">
                    <input type="hidden" id="memberId">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="memberName" class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                            <input type="text" id="memberName" name="name" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="memberGender" class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                            <select id="memberGender" name="gender" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="unknown">未知</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="memberBirthDate" class="block text-sm font-medium text-gray-700 mb-2">出生日期</label>
                            <input type="date" id="memberBirthDate" name="birthDate"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="memberDeathDate" class="block text-sm font-medium text-gray-700 mb-2">死亡日期</label>
                            <input type="date" id="memberDeathDate" name="deathDate"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="memberGeneration" class="block text-sm font-medium text-gray-700 mb-2">辈分</label>
                            <input type="text" id="memberGeneration" name="generationName"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="如：第一代、第二代等">
                        </div>
                        
                        <div>
                            <label for="memberPhotoUrl" class="block text-sm font-medium text-gray-700 mb-2">照片URL</label>
                            <input type="url" id="memberPhotoUrl" name="photoUrl"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="https://example.com/photo.jpg">
                        </div>
                    </div>
                    
                    <div>
                        <label for="memberDescription" class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea id="memberDescription" name="description" rows="3"
                                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="关于此成员的描述信息..."></textarea>
                    </div>
                    
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">家庭关系</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="memberFather" class="block text-sm font-medium text-gray-700 mb-2">父亲</label>
                                <select id="memberFather" name="fatherId" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">选择父亲</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="memberMother" class="block text-sm font-medium text-gray-700 mb-2">母亲</label>
                                <select id="memberMother" name="motherId" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">选择母亲</option>
                                </select>
                            </div>
                            
                            <div class="md:col-span-2">
                                <label for="memberSpouse" class="block text-sm font-medium text-gray-700 mb-2">配偶</label>
                                <select id="memberSpouse" name="spouseId" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">选择配偶</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <button type="button" id="cancelBtn" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                            取消
                        </button>
                        <button type="submit" id="saveBtn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <span id="saveBtnText">保存</span>
                            <i id="saveSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-md w-full">
            <div class="p-6">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
                    <p class="text-sm text-gray-500 mb-6">您确定要删除这个成员吗？此操作无法撤销。</p>
                    <div class="flex justify-center space-x-3">
                        <button id="cancelDeleteBtn" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                            取消
                        </button>
                        <button id="confirmDeleteBtn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示消息 -->
    <div id="toast" class="hidden fixed top-4 right-4 z-50 max-w-sm">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4">
            <div class="flex items-center">
                <div id="toastIcon" class="flex-shrink-0 mr-3">
                    <!-- 动态图标 -->
                </div>
                <div id="toastMessage" class="text-sm font-medium text-gray-900">
                    <!-- 动态消息 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let membersData = [];
        let filteredMembers = [];
        let currentPage = 1;
        let pageSize = 12;
        let editingMemberId = null;
        
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const username = localStorage.getItem('username');
            
            if (!token) {
                window.location.href = '/login';
                return false;
            }
            
            if (username) {
                document.getElementById('usernameDisplay').textContent = username;
            }
            
            return true;
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            window.location.href = '/login?logout=true';
        }
        
        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('token');
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
        }
        
        // 显示提示消息
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const icon = document.getElementById('toastIcon');
            const messageEl = document.getElementById('toastMessage');
            
            const icons = {
                success: '<i class="fas fa-check-circle text-green-500"></i>',
                error: '<i class="fas fa-exclamation-circle text-red-500"></i>',
                warning: '<i class="fas fa-exclamation-triangle text-yellow-500"></i>',
                info: '<i class="fas fa-info-circle text-blue-500"></i>'
            };
            
            icon.innerHTML = icons[type] || icons.info;
            messageEl.textContent = message;
            
            toast.classList.remove('hidden');
            toast.classList.add('animate__animated', 'animate__fadeInRight');
            
            setTimeout(() => {
                toast.classList.add('animate__fadeOutRight');
                setTimeout(() => {
                    toast.classList.add('hidden');
                    toast.classList.remove('animate__animated', 'animate__fadeInRight', 'animate__fadeOutRight');
                }, 300);
            }, 3000);
        }
        
        // 加载成员数据
        async function loadMembers() {
            try {
                const response = await fetch('/api/family-members', {
                    headers: getAuthHeaders()
                });
                
                if (response.status === 401) {
                    logout();
                    return;
                }
                
                if (response.ok) {
                    membersData = await response.json();
                    filteredMembers = [...membersData];
                    renderMembers();
                    loadGenerationFilter();
                    loadRelationshipOptions();
                } else {
                    throw new Error('加载数据失败');
                }
            } catch (error) {
                console.error('加载成员数据失败:', error);
                showToast('加载成员数据失败，请刷新页面重试', 'error');
            } finally {
                document.getElementById('loadingSpinner').classList.add('hidden');
            }
        }
        
        // 加载辈分筛选选项
        async function loadGenerationFilter() {
            try {
                const response = await fetch('/api/family-members/generations', {
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const generations = await response.json();
                    const select = document.getElementById('generationFilter');
                    
                    // 清空现有选项（保留"所有辈分"）
                    select.innerHTML = '<option value="">所有辈分</option>';
                    
                    generations.forEach(generation => {
                        const option = document.createElement('option');
                        option.value = generation;
                        option.textContent = generation;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载辈分数据失败:', error);
            }
        }
        
        // 加载关系选项
        function loadRelationshipOptions() {
            const fatherSelect = document.getElementById('memberFather');
            const motherSelect = document.getElementById('memberMother');
            const spouseSelect = document.getElementById('memberSpouse');
            
            // 清空现有选项
            fatherSelect.innerHTML = '<option value="">选择父亲</option>';
            motherSelect.innerHTML = '<option value="">选择母亲</option>';
            spouseSelect.innerHTML = '<option value="">选择配偶</option>';
            
            membersData.forEach(member => {
                if (editingMemberId && member.id === editingMemberId) return;
                
                // 父亲选项（男性）
                if (member.gender === 'male') {
                    const option = document.createElement('option');
                    option.value = member.id;
                    option.textContent = member.name;
                    fatherSelect.appendChild(option);
                }
                
                // 母亲选项（女性）
                if (member.gender === 'female') {
                    const option = document.createElement('option');
                    option.value = member.id;
                    option.textContent = member.name;
                    motherSelect.appendChild(option);
                }
                
                // 配偶选项（所有性别）
                const spouseOption = document.createElement('option');
                spouseOption.value = member.id;
                spouseOption.textContent = member.name;
                spouseSelect.appendChild(spouseOption);
            });
        }
        
        // 渲染成员列表
        function renderMembers() {
            const container = document.getElementById('membersContainer');
            const emptyState = document.getElementById('emptyState');
            const membersGrid = document.getElementById('membersGrid');
            
            if (filteredMembers.length === 0) {
                membersGrid.classList.add('hidden');
                emptyState.classList.remove('hidden');
                return;
            }
            
            emptyState.classList.add('hidden');
            membersGrid.classList.remove('hidden');
            
            // 分页
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageMembers = filteredMembers.slice(startIndex, endIndex);
            
            // 生成成员卡片
            container.innerHTML = pageMembers.map(member => createMemberCard(member)).join('');
            
            // 渲染分页
            renderPagination();
        }
        
        // 创建成员卡片
        function createMemberCard(member) {
            const genderIcon = member.gender === 'male' ? 'fa-mars text-blue-500' : 
                              member.gender === 'female' ? 'fa-venus text-pink-500' : 'fa-question text-gray-500';
            const genderText = member.gender === 'male' ? '男' : member.gender === 'female' ? '女' : '未知';
            
            const birthYear = member.birthDate ? new Date(member.birthDate).getFullYear() : '';
            const deathYear = member.deathDate ? new Date(member.deathDate).getFullYear() : '';
            const lifeSpan = birthYear ? (deathYear ? `${birthYear}-${deathYear}` : `${birthYear}-`) : '';
            
            return `
                <div class="member-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                                ${member.photoUrl ? 
                                    `<img src="${member.photoUrl}" alt="${member.name}" class="w-full h-full rounded-full object-cover">` :
                                    `<i class="fas ${genderIcon} text-xl"></i>`
                                }
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${member.name}</h3>
                                <p class="text-sm text-gray-500">${genderText}</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="editMember(${member.id})" class="text-blue-600 hover:text-blue-800 p-1">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteMember(${member.id})" class="text-red-600 hover:text-red-800 p-1">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="space-y-2 text-sm">
                        ${lifeSpan ? `<p class="text-gray-600"><i class="fas fa-calendar mr-2"></i>${lifeSpan}</p>` : ''}
                        ${member.generationName ? `<p class="text-gray-600"><i class="fas fa-layer-group mr-2"></i>${member.generationName}</p>` : ''}
                        ${member.description ? `<p class="text-gray-600 truncate"><i class="fas fa-info-circle mr-2"></i>${member.description}</p>` : ''}
                    </div>
                    
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="flex justify-between text-xs text-gray-500">
                            <span>ID: ${member.id}</span>
                            <span>${new Date(member.createdAt).toLocaleDateString('zh-CN')}</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 渲染分页
        function renderPagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredMembers.length / pageSize);
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '<div class="flex items-center space-x-2">';
            
            // 上一页
            if (currentPage > 1) {
                html += `<button onclick="changePage(${currentPage - 1})" class="px-3 py-2 text-gray-500 hover:text-blue-600 border border-gray-300 rounded-lg hover:border-blue-300">
                    <i class="fas fa-chevron-left"></i>
                </button>`;
            }
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    html += `<button class="px-3 py-2 bg-blue-600 text-white border border-blue-600 rounded-lg">${i}</button>`;
                } else {
                    html += `<button onclick="changePage(${i})" class="px-3 py-2 text-gray-500 hover:text-blue-600 border border-gray-300 rounded-lg hover:border-blue-300">${i}</button>`;
                }
            }
            
            // 下一页
            if (currentPage < totalPages) {
                html += `<button onclick="changePage(${currentPage + 1})" class="px-3 py-2 text-gray-500 hover:text-blue-600 border border-gray-300 rounded-lg hover:border-blue-300">
                    <i class="fas fa-chevron-right"></i>
                </button>`;
            }
            
            html += '</div>';
            pagination.innerHTML = html;
        }
        
        // 切换页面
        function changePage(page) {
            currentPage = page;
            renderMembers();
        }
        
        // 筛选成员
        function filterMembers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const selectedGeneration = document.getElementById('generationFilter').value;
            const selectedGender = document.getElementById('genderFilter').value;
            
            filteredMembers = membersData.filter(member => {
                const matchesSearch = !searchTerm || 
                    member.name.toLowerCase().includes(searchTerm) ||
                    (member.description && member.description.toLowerCase().includes(searchTerm));
                
                const matchesGeneration = !selectedGeneration || member.generationName === selectedGeneration;
                const matchesGender = !selectedGender || member.gender === selectedGender;
                
                return matchesSearch && matchesGeneration && matchesGender;
            });
            
            currentPage = 1;
            renderMembers();
        }
        
        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('generationFilter').value = '';
            document.getElementById('genderFilter').value = '';
            filteredMembers = [...membersData];
            currentPage = 1;
            renderMembers();
        }
        
        // 显示添加成员模态框
        function showAddMemberModal() {
            editingMemberId = null;
            document.getElementById('modalTitle').textContent = '添加成员';
            document.getElementById('memberForm').reset();
            document.getElementById('memberId').value = '';
            loadRelationshipOptions();
            document.getElementById('memberModal').classList.remove('hidden');
        }
        
        // 编辑成员
        function editMember(id) {
            const member = membersData.find(m => m.id === id);
            if (!member) return;
            
            editingMemberId = id;
            document.getElementById('modalTitle').textContent = '编辑成员';
            document.getElementById('memberId').value = member.id;
            document.getElementById('memberName').value = member.name || '';
            document.getElementById('memberGender').value = member.gender || 'unknown';
            document.getElementById('memberBirthDate').value = member.birthDate || '';
            document.getElementById('memberDeathDate').value = member.deathDate || '';
            document.getElementById('memberGeneration').value = member.generationName || '';
            document.getElementById('memberPhotoUrl').value = member.photoUrl || '';
            document.getElementById('memberDescription').value = member.description || '';
            
            loadRelationshipOptions();
            
            // 设置关系
            setTimeout(() => {
                document.getElementById('memberFather').value = member.fatherId || '';
                document.getElementById('memberMother').value = member.motherId || '';
                document.getElementById('memberSpouse').value = member.spouseId || '';
            }, 100);
            
            document.getElementById('memberModal').classList.remove('hidden');
        }
        
        // 删除成员
        function deleteMember(id) {
            const member = membersData.find(m => m.id === id);
            if (!member) return;
            
            document.getElementById('deleteModal').classList.remove('hidden');
            
            document.getElementById('confirmDeleteBtn').onclick = async () => {
                try {
                    const response = await fetch(`/api/family-members/${id}`, {
                        method: 'DELETE',
                        headers: getAuthHeaders()
                    });
                    
                    if (response.ok) {
                        showToast('成员删除成功');
                        loadMembers();
                    } else {
                        const error = await response.json();
                        showToast(error.message || '删除失败', 'error');
                    }
                } catch (error) {
                    console.error('删除成员失败:', error);
                    showToast('删除失败，请稍后重试', 'error');
                } finally {
                    document.getElementById('deleteModal').classList.add('hidden');
                }
            };
        }
        
        // 保存成员
        async function saveMember(formData) {
            const saveBtn = document.getElementById('saveBtn');
            const saveBtnText = document.getElementById('saveBtnText');
            const saveSpinner = document.getElementById('saveSpinner');
            
            saveBtn.disabled = true;
            saveBtnText.textContent = editingMemberId ? '更新中...' : '保存中...';
            saveSpinner.classList.remove('hidden');
            
            try {
                const url = editingMemberId ? `/api/family-members/${editingMemberId}` : '/api/family-members';
                const method = editingMemberId ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: getAuthHeaders(),
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    showToast(editingMemberId ? '成员更新成功' : '成员添加成功');
                    document.getElementById('memberModal').classList.add('hidden');
                    loadMembers();
                } else {
                    const error = await response.json();
                    showToast(error.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存成员失败:', error);
                showToast('保存失败，请稍后重试', 'error');
            } finally {
                saveBtn.disabled = false;
                saveBtnText.textContent = editingMemberId ? '更新' : '保存';
                saveSpinner.classList.add('hidden');
            }
        }
        
        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkAuth()) return;
            
            // 加载数据
            loadMembers();
            
            // 搜索和筛选
            document.getElementById('searchInput').addEventListener('input', filterMembers);
            document.getElementById('generationFilter').addEventListener('change', filterMembers);
            document.getElementById('genderFilter').addEventListener('change', filterMembers);
            document.getElementById('resetFiltersBtn').addEventListener('click', resetFilters);
            
            // 添加成员按钮
            document.getElementById('addMemberBtn').addEventListener('click', showAddMemberModal);
            document.getElementById('addFirstMemberBtn').addEventListener('click', showAddMemberModal);
            
            // 模态框关闭
            document.getElementById('closeMemberModal').addEventListener('click', () => {
                document.getElementById('memberModal').classList.add('hidden');
            });
            
            document.getElementById('cancelBtn').addEventListener('click', () => {
                document.getElementById('memberModal').classList.add('hidden');
            });
            
            document.getElementById('cancelDeleteBtn').addEventListener('click', () => {
                document.getElementById('deleteModal').classList.add('hidden');
            });
            
            // 表单提交
            document.getElementById('memberForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = {
                    name: document.getElementById('memberName').value.trim(),
                    gender: document.getElementById('memberGender').value,
                    birthDate: document.getElementById('memberBirthDate').value || null,
                    deathDate: document.getElementById('memberDeathDate').value || null,
                    generationName: document.getElementById('memberGeneration').value.trim() || null,
                    photoUrl: document.getElementById('memberPhotoUrl').value.trim() || null,
                    description: document.getElementById('memberDescription').value.trim() || null,
                    fatherId: document.getElementById('memberFather').value || null,
                    motherId: document.getElementById('memberMother').value || null,
                    spouseId: document.getElementById('memberSpouse').value || null
                };
                
                if (!formData.name) {
                    showToast('请输入成员姓名', 'error');
                    return;
                }
                
                saveMember(formData);
            });
            
            // 用户菜单
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                document.getElementById('userMenu').classList.toggle('hidden');
            });
            
            document.addEventListener('click', function() {
                document.getElementById('userMenu').classList.add('hidden');
            });
            
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
            
            // 检查URL参数（编辑模式）
            const urlParams = new URLSearchParams(window.location.search);
            const editId = urlParams.get('edit');
            if (editId) {
                setTimeout(() => {
                    editMember(parseInt(editId));
                }, 1000);
            }
        });
        
        // 全局函数
        window.editMember = editMember;
        window.deleteMember = deleteMember;
        window.changePage = changePage;
    </script>
</body>
</html>
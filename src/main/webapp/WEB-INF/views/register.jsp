<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link href="/static/css/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .register-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="register-bg min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- 注册卡片 -->
        <div class="bg-white rounded-2xl shadow-2xl p-8 animate__animated animate__fadeInUp">
            <!-- 头部 -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                    <i class="fas fa-user-plus text-2xl text-green-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">创建账户</h1>
                <p class="text-gray-600">加入族谱管理系统</p>
            </div>

            <!-- 注册表单 -->
            <form id="registerForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2"></i>用户名
                    </label>
                    <input type="text" id="username" name="username" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                           placeholder="请输入用户名（3-20个字符）">
                    <p class="text-xs text-gray-500 mt-1">用户名长度为3-20个字符，只能包含字母、数字和下划线</p>
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2"></i>密码
                    </label>
                    <div class="relative">
                        <input type="password" id="password" name="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all pr-12"
                               placeholder="请输入密码（至少6位）">
                        <button type="button" id="togglePassword" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <div class="flex items-center space-x-2 text-xs">
                            <div id="lengthCheck" class="flex items-center text-gray-400">
                                <i class="fas fa-circle mr-1"></i>
                                <span>至少6个字符</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2"></i>确认密码
                    </label>
                    <div class="relative">
                        <input type="password" id="confirmPassword" name="confirmPassword" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all pr-12"
                               placeholder="请再次输入密码">
                        <button type="button" id="toggleConfirmPassword" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div id="passwordMatch" class="mt-1 text-xs text-gray-500"></div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="agreement" name="agreement" required class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                    <label for="agreement" class="ml-2 text-sm text-gray-600">
                        我已阅读并同意 <a href="#" class="text-green-600 hover:text-green-800">用户协议</a> 和 <a href="#" class="text-green-600 hover:text-green-800">隐私政策</a>
                    </label>
                </div>

                <button type="submit" id="registerBtn"
                        class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                    <span id="registerBtnText">注册</span>
                    <i id="registerSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                </button>
            </form>

            <!-- 分割线 -->
            <div class="mt-8 text-center">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">或者</span>
                    </div>
                </div>
            </div>

            <!-- 登录链接 -->
            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    已有账户？
                    <a href="/login" class="text-green-600 hover:text-green-800 font-medium">立即登录</a>
                </p>
            </div>

            <!-- 返回首页 -->
            <div class="mt-4 text-center">
                <a href="/" class="text-gray-500 hover:text-gray-700 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>返回首页
                </a>
            </div>
        </div>

        <!-- 错误提示 -->
        <div id="errorAlert" class="hidden mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg animate__animated animate__fadeInDown">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span id="errorMessage"></span>
            </div>
        </div>

        <!-- 成功提示 -->
        <div id="successAlert" class="hidden mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg animate__animated animate__fadeInDown">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span id="successMessage"></span>
            </div>
        </div>
    </div>

    <script>
        // 密码显示/隐藏切换
        function setupPasswordToggle(inputId, toggleId) {
            document.getElementById(toggleId).addEventListener('click', function() {
                const passwordInput = document.getElementById(inputId);
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        setupPasswordToggle('password', 'togglePassword');
        setupPasswordToggle('confirmPassword', 'toggleConfirmPassword');

        // 显示错误信息
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            const successAlert = document.getElementById('successAlert');
            
            successAlert.classList.add('hidden');
            errorMessage.textContent = message;
            errorAlert.classList.remove('hidden');
            
            setTimeout(() => {
                errorAlert.classList.add('hidden');
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            const successAlert = document.getElementById('successAlert');
            const successMessage = document.getElementById('successMessage');
            const errorAlert = document.getElementById('errorAlert');
            
            errorAlert.classList.add('hidden');
            successMessage.textContent = message;
            successAlert.classList.remove('hidden');
        }

        // 设置加载状态
        function setLoading(loading) {
            const registerBtn = document.getElementById('registerBtn');
            const registerBtnText = document.getElementById('registerBtnText');
            const registerSpinner = document.getElementById('registerSpinner');
            
            if (loading) {
                registerBtn.disabled = true;
                registerBtn.classList.add('opacity-75', 'cursor-not-allowed');
                registerBtnText.textContent = '注册中...';
                registerSpinner.classList.remove('hidden');
            } else {
                registerBtnText.textContent = '注册';
                registerSpinner.classList.add('hidden');
                validateForm(); // 重新验证表单
            }
        }

        // 验证用户名
        function validateUsername(username) {
            const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
            return usernameRegex.test(username);
        }

        // 验证密码强度
        function validatePassword(password) {
            const lengthCheck = document.getElementById('lengthCheck');
            const lengthValid = password.length >= 6;
            
            if (lengthValid) {
                lengthCheck.classList.remove('text-gray-400');
                lengthCheck.classList.add('text-green-600');
                lengthCheck.querySelector('i').classList.remove('fa-circle');
                lengthCheck.querySelector('i').classList.add('fa-check-circle');
            } else {
                lengthCheck.classList.remove('text-green-600');
                lengthCheck.classList.add('text-gray-400');
                lengthCheck.querySelector('i').classList.remove('fa-check-circle');
                lengthCheck.querySelector('i').classList.add('fa-circle');
            }
            
            return lengthValid;
        }

        // 验证密码匹配
        function validatePasswordMatch(password, confirmPassword) {
            const passwordMatch = document.getElementById('passwordMatch');
            
            if (confirmPassword === '') {
                passwordMatch.textContent = '';
                return false;
            }
            
            if (password === confirmPassword) {
                passwordMatch.textContent = '密码匹配';
                passwordMatch.classList.remove('text-red-500');
                passwordMatch.classList.add('text-green-600');
                return true;
            } else {
                passwordMatch.textContent = '密码不匹配';
                passwordMatch.classList.remove('text-green-600');
                passwordMatch.classList.add('text-red-500');
                return false;
            }
        }

        // 验证表单
        function validateForm() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreement = document.getElementById('agreement').checked;
            const registerBtn = document.getElementById('registerBtn');
            
            const usernameValid = validateUsername(username);
            const passwordValid = validatePassword(password);
            const passwordMatchValid = validatePasswordMatch(password, confirmPassword);
            
            const formValid = usernameValid && passwordValid && passwordMatchValid && agreement;
            
            registerBtn.disabled = !formValid;
            if (formValid) {
                registerBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                registerBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // 添加事件监听器
        document.getElementById('username').addEventListener('input', validateForm);
        document.getElementById('password').addEventListener('input', validateForm);
        document.getElementById('confirmPassword').addEventListener('input', validateForm);
        document.getElementById('agreement').addEventListener('change', validateForm);

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!validateUsername(username)) {
                showError('用户名格式不正确，请输入3-20个字符的用户名，只能包含字母、数字和下划线');
                return;
            }
            
            if (!validatePassword(password)) {
                showError('密码长度至少为6个字符');
                return;
            }
            
            if (password !== confirmPassword) {
                showError('两次输入的密码不一致');
                return;
            }
            
            setLoading(true);
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showSuccess('注册成功！正在跳转到登录页面...');
                    
                    // 跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/login?registered=true';
                    }, 2000);
                } else {
                    showError(data.message || '注册失败，请稍后重试');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showError('网络错误，请稍后重试');
            } finally {
                setLoading(false);
            }
        });

        // 初始验证
        validateForm();
    </script>
</body>
</html>
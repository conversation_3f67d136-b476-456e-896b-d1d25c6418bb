/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.modal-body {
    padding: 2rem;
}

/* 族谱图样式 */
.family-tree {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
}

.generation {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    flex-wrap: wrap;
    gap: 2rem;
}

.member {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    min-width: 200px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.member:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.member.male {
    border-left: 5px solid #3498db;
}

.member.female {
    border-left: 5px solid #e91e63;
}

.member.unknown {
    border-left: 5px solid #95a5a6;
}

.member-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-photo i {
    font-size: 2rem;
    color: #6c757d;
}

.member-name {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.member-info {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.member-generation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
    margin-top: 0.5rem;
}

/* 连接线样式 */
.connection-line {
    position: absolute;
    background: #ddd;
    z-index: 1;
}

.connection-line.horizontal {
    height: 2px;
}

.connection-line.vertical {
    width: 2px;
}

/* 统计卡片样式 */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stats-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.stats-icon.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.info {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
}

/* 搜索框样式 */
.search-container {
    position: relative;
    margin-bottom: 2rem;
}

.search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.2rem;
}

/* 筛选器样式 */
.filter-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.filter-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    transition: all 0.3s ease;
}

.filter-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    background: white;
    color: #6c757d;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.pagination-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #495057;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* 提示消息样式 */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 9999;
    max-width: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.toast.success {
    border-left: 5px solid #28a745;
}

.toast.error {
    border-left: 5px solid #dc3545;
}

.toast.warning {
    border-left: 5px solid #ffc107;
}

.toast.info {
    border-left: 5px solid #17a2b8;
}

.toast-icon {
    font-size: 1.5rem;
}

.toast-icon.success {
    color: #28a745;
}

.toast-icon.error {
    color: #dc3545;
}

.toast-icon.warning {
    color: #ffc107;
}

.toast-icon.info {
    color: #17a2b8;
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .member {
        min-width: 150px;
        padding: 1rem;
    }
    
    .member-photo {
        width: 60px;
        height: 60px;
    }
    
    .stats-card {
        padding: 1.5rem;
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .generation {
        gap: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .member {
        min-width: 120px;
        padding: 0.75rem;
    }
    
    .member-photo {
        width: 50px;
        height: 50px;
    }
    
    .member-photo i {
        font-size: 1.5rem;
    }
    
    .member-name {
        font-size: 1rem;
    }
    
    .member-info {
        font-size: 0.8rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .stats-label {
        font-size: 1rem;
    }
    
    .search-input {
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        font-size: 1rem;
    }
    
    .search-icon {
        left: 0.75rem;
        font-size: 1rem;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
// 公共JavaScript函数库

// API基础URL
const API_BASE_URL = '/api';

// 本地存储键名
const STORAGE_KEYS = {
    TOKEN: 'family_tree_token',
    USER: 'family_tree_user'
};

// 工具函数
const Utils = {
    // 获取JWT令牌
    getToken() {
        return localStorage.getItem(STORAGE_KEYS.TOKEN);
    },

    // 设置JWT令牌
    setToken(token) {
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
    },

    // 移除JWT令牌
    removeToken() {
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
    },

    // 获取用户信息
    getUser() {
        const userStr = localStorage.getItem(STORAGE_KEYS.USER);
        return userStr ? JSON.parse(userStr) : null;
    },

    // 设置用户信息
    setUser(user) {
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    },

    // 移除用户信息
    removeUser() {
        localStorage.removeItem(STORAGE_KEYS.USER);
    },

    // 检查是否已登录
    isLoggedIn() {
        return !!this.getToken();
    },

    // 退出登录
    logout() {
        this.removeToken();
        this.removeUser();
        window.location.href = '/login';
    },

    // 格式化日期
    formatDate(dateStr) {
        if (!dateStr) return '未知';
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN');
    },

    // 计算年龄
    calculateAge(birthDate, deathDate = null) {
        if (!birthDate) return '未知';
        
        const birth = new Date(birthDate);
        const end = deathDate ? new Date(deathDate) : new Date();
        
        let age = end.getFullYear() - birth.getFullYear();
        const monthDiff = end.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && end.getDate() < birth.getDate())) {
            age--;
        }
        
        return age >= 0 ? age : 0;
    },

    // 获取性别显示文本
    getGenderText(gender) {
        switch (gender) {
            case 'MALE': return '男';
            case 'FEMALE': return '女';
            default: return '未知';
        }
    },

    // 获取性别图标
    getGenderIcon(gender) {
        switch (gender) {
            case 'MALE': return 'fas fa-mars';
            case 'FEMALE': return 'fas fa-venus';
            default: return 'fas fa-question';
        }
    },

    // 获取性别CSS类
    getGenderClass(gender) {
        switch (gender) {
            case 'MALE': return 'male';
            case 'FEMALE': return 'female';
            default: return 'unknown';
        }
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 生成随机ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // 深拷贝对象
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    // 验证邮箱格式
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // 验证手机号格式
    validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    },

    // 验证密码强度
    validatePassword(password) {
        const result = {
            isValid: false,
            strength: 0,
            messages: []
        };

        if (password.length < 6) {
            result.messages.push('密码长度至少6位');
        } else {
            result.strength += 1;
        }

        if (!/[a-z]/.test(password)) {
            result.messages.push('密码需包含小写字母');
        } else {
            result.strength += 1;
        }

        if (!/[A-Z]/.test(password)) {
            result.messages.push('密码需包含大写字母');
        } else {
            result.strength += 1;
        }

        if (!/\d/.test(password)) {
            result.messages.push('密码需包含数字');
        } else {
            result.strength += 1;
        }

        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            result.messages.push('密码需包含特殊字符');
        } else {
            result.strength += 1;
        }

        result.isValid = result.strength >= 3;
        return result;
    }
};

// HTTP请求工具
const Http = {
    // 通用请求方法
    async request(url, options = {}) {
        const token = Utils.getToken();
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            
            // 处理401未授权错误
            if (response.status === 401) {
                Utils.logout();
                return;
            }

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }

            return data;
        } catch (error) {
            console.error('HTTP请求错误:', error);
            throw error;
        }
    },

    // GET请求
    async get(url) {
        return this.request(url, { method: 'GET' });
    },

    // POST请求
    async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    // PUT请求
    async put(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    // DELETE请求
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }
};

// 提示消息工具
const Toast = {
    // 显示提示消息
    show(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type} fade-in`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <i class="toast-icon ${type} ${iconMap[type]}"></i>
            <div class="toast-message">${message}</div>
        `;

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    },

    // 成功提示
    success(message, duration) {
        this.show(message, 'success', duration);
    },

    // 错误提示
    error(message, duration) {
        this.show(message, 'error', duration);
    },

    // 警告提示
    warning(message, duration) {
        this.show(message, 'warning', duration);
    },

    // 信息提示
    info(message, duration) {
        this.show(message, 'info', duration);
    }
};

// 加载状态管理
const Loading = {
    // 显示加载状态
    show(container = document.body) {
        const loading = document.createElement('div');
        loading.className = 'loading-spinner';
        loading.innerHTML = '<div class="spinner"></div>';
        loading.id = 'global-loading';
        
        container.appendChild(loading);
    },

    // 隐藏加载状态
    hide() {
        const loading = document.getElementById('global-loading');
        if (loading) {
            loading.parentNode.removeChild(loading);
        }
    }
};

// 模态框工具
const Modal = {
    // 显示确认对话框
    confirm(title, message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
        
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-action="cancel">取消</button>
                        <button type="button" class="btn btn-danger" data-action="confirm">确认</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        modal.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'confirm') {
                if (onConfirm) onConfirm();
                this.close(modal);
            } else if (e.target.dataset.action === 'cancel' || e.target === modal) {
                if (onCancel) onCancel();
                this.close(modal);
            }
        });

        return modal;
    },

    // 关闭模态框
    close(modal) {
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }
};

// 表单验证工具
const Validator = {
    // 验证必填字段
    required(value, message = '此字段为必填项') {
        return {
            isValid: value !== null && value !== undefined && value.toString().trim() !== '',
            message
        };
    },

    // 验证最小长度
    minLength(value, min, message) {
        const isValid = value && value.toString().length >= min;
        return {
            isValid,
            message: message || `最少需要${min}个字符`
        };
    },

    // 验证最大长度
    maxLength(value, max, message) {
        const isValid = !value || value.toString().length <= max;
        return {
            isValid,
            message: message || `最多允许${max}个字符`
        };
    },

    // 验证邮箱
    email(value, message = '请输入有效的邮箱地址') {
        return {
            isValid: !value || Utils.validateEmail(value),
            message
        };
    },

    // 验证手机号
    phone(value, message = '请输入有效的手机号码') {
        return {
            isValid: !value || Utils.validatePhone(value),
            message
        };
    },

    // 验证表单
    validateForm(formData, rules) {
        const errors = {};
        let isValid = true;

        for (const field in rules) {
            const fieldRules = rules[field];
            const value = formData[field];

            for (const rule of fieldRules) {
                const result = rule(value);
                if (!result.isValid) {
                    errors[field] = result.message;
                    isValid = false;
                    break;
                }
            }
        }

        return { isValid, errors };
    }
};

// 分页工具
const Pagination = {
    // 创建分页组件
    create(container, currentPage, totalPages, onPageChange) {
        container.innerHTML = '';

        if (totalPages <= 1) return;

        const pagination = document.createElement('div');
        pagination.className = 'pagination';

        // 上一页按钮
        const prevBtn = document.createElement('button');
        prevBtn.className = 'pagination-btn';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.disabled = currentPage === 1;
        prevBtn.onclick = () => {
            if (currentPage > 1) onPageChange(currentPage - 1);
        };
        pagination.appendChild(prevBtn);

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            const firstBtn = document.createElement('button');
            firstBtn.className = 'pagination-btn';
            firstBtn.textContent = '1';
            firstBtn.onclick = () => onPageChange(1);
            pagination.appendChild(firstBtn);

            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'pagination-ellipsis';
                pagination.appendChild(ellipsis);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => onPageChange(i);
            pagination.appendChild(pageBtn);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'pagination-ellipsis';
                pagination.appendChild(ellipsis);
            }

            const lastBtn = document.createElement('button');
            lastBtn.className = 'pagination-btn';
            lastBtn.textContent = totalPages;
            lastBtn.onclick = () => onPageChange(totalPages);
            pagination.appendChild(lastBtn);
        }

        // 下一页按钮
        const nextBtn = document.createElement('button');
        nextBtn.className = 'pagination-btn';
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.onclick = () => {
            if (currentPage < totalPages) onPageChange(currentPage + 1);
        };
        pagination.appendChild(nextBtn);

        container.appendChild(pagination);
    }
};

// 导出到全局
window.Utils = Utils;
window.Http = Http;
window.Toast = Toast;
window.Loading = Loading;
window.Modal = Modal;
window.Validator = Validator;
window.Pagination = Pagination;
window.API_BASE_URL = API_BASE_URL;
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .feature {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .tech-item {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌳 家族族谱管理系统</h1>
        
        <div class="status success">
            ✅ 项目重构完成！已成功转换为 Spring Boot 架构
        </div>
        
        <div class="status info">
            ℹ️ 当前状态：Maven 正在下载依赖，请稍等片刻...
        </div>
        
        <h2>🚀 技术栈</h2>
        <div class="tech-stack">
            <div class="tech-item">
                <strong>Spring Boot 2.7.0</strong><br>
                主框架
            </div>
            <div class="tech-item">
                <strong>MyBatis</strong><br>
                ORM框架
            </div>
            <div class="tech-item">
                <strong>MySQL 8.0</strong><br>
                数据库
            </div>
            <div class="tech-item">
                <strong>JSP + JSTL</strong><br>
                视图层
            </div>
            <div class="tech-item">
                <strong>Tailwind CSS</strong><br>
                样式框架
            </div>
            <div class="tech-item">
                <strong>Maven</strong><br>
                项目管理
            </div>
        </div>
        
        <h2>📋 功能特性</h2>
        <div class="feature">
            <strong>👥 成员管理</strong> - 添加、编辑、删除家族成员信息
        </div>
        <div class="feature">
            <strong>🌳 族谱图</strong> - 可视化家族关系展示
        </div>
        <div class="feature">
            <strong>📊 辈分管理</strong> - 按辈分统计和管理成员
        </div>
        <div class="feature">
            <strong>🔐 用户系统</strong> - 注册、登录、JWT认证
        </div>
        <div class="feature">
            <strong>📱 响应式设计</strong> - 支持PC和移动端访问
        </div>
        
        <h2>🎯 下一步操作</h2>
        <ol>
            <li>等待 Maven 依赖下载完成</li>
            <li>配置 MySQL 数据库连接</li>
            <li>执行数据库初始化脚本</li>
            <li>启动应用并访问 <code>http://localhost:8080</code></li>
        </ol>
        
        <div class="status info">
            💡 默认登录账户：admin / admin123
        </div>
    </div>
</body>
</html>